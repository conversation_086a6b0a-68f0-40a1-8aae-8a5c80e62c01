name = "claude-relay-backend"
main = "src/index.ts"
compatibility_date = "2025-07-25"

# 管理中心统一 KV 存储（存储账号、配置、tokens等所有数据）
[[kv_namespaces]]
binding = "KV"
id = "5ec8c36e2bfd416f9061adc42d9c3bec"
preview_id = "7542a354766e48f09da8560f7318b56b"

# 定时任务配置 - 每6小时刷新一次 token
[triggers]
crons = ["0 */6 * * *"]

# 生产环境配置
[vars]
NODE_ENV = "production"
FRONTEND_URL = "https://ccr.iphoi.com"
# 管理员认证
ADMIN_USERNAME = "ruohan2016"
ADMIN_PASSWORD = "zhu201626"

# 预览环境配置
[env.preview]
name = "claude-relay-backend-preview"

[env.preview.vars]
NODE_ENV = "preview"
FRONTEND_URL = "https://ccv.iphoi.com"
# 管理员认证
ADMIN_USERNAME = "ruohan2016"
ADMIN_PASSWORD = "zhu201626"