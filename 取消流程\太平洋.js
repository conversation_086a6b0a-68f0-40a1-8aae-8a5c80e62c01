/**
 * 
 * 项目类型：APP
 * 项目名称：太平洋汽车
 * 项目抓包：手机号 & 密码
 * 项目变量：tpyqc
 * 项目定时：每天运行一次
 * cron: 12 8,12,15 * * *
 * 
 * 脚本更新时间：2025-03-31
 * 更新日志：新增脚本执行延迟，随机时间
 * 
 * 脚本说明：
 * 1、怕被封号不想发帖的可以把40行的issueFlag设置为false
 * 
 */


//===============脚本版本=================//
const $ = new Env('太平洋汽车');
const axios = require('axios');
const dayjs = require("dayjs");
let request = require("request");
request = request.defaults({
    jar: true
});
const {
    log
} = console;
const debug = 0; //0为关闭调试，1为打开调试,默认为0
const Notify = 1; //0为关闭通知，1为打开通知,默认为1

let tpyqc = ($.isNode() ? process.env.tpyqc : $.getdata("tpyqc")) || ""
let tpyqcAccount = [
    "***********&a826294082.",
    "***********&a826294082."
]
if(tpyqcAccount.length > 0){
    tpyqc = tpyqcAccount.join("@")
}
let tpyqcArr = [];
let host = 'mrobot.pcauto.com.cn';
let hostname = 'https://' + host;
let msg = "";

// 是否发布动态
const issueFlag = false;

!(async() => {
    if (typeof $request !== "undefined") {
        await GetRewrite();
    } else {
        if (!(await Envs()))
            return;
        else {

            log(`\n\n=============================================    \n脚本执行 - 北京时间(UTC+8)：${new Date(
                new Date().getTime() + new Date().getTimezoneOffset() * 60 * 1000 +
                8 * 60 * 60 * 1000).toLocaleString()} \n=============================================\n`);
            log(`\n============ 微信小程序：柠檬玩机 ============`)
            log(`\n=================== 共找到 ${tpyqcArr.length} 个账号 ===================`)
            if (debug) {
                log(`【debug】 这是你的全部账号数组:\n ${tpyqcArr}`);
            }
            // 设置随机等待时间范围
            const waitRanges = [
                { min: 180, max: 240 }, // 3-4分钟
                { min: 290, max: 370 }, // 约5-6分钟
                { min: 380, max: 410 }, // 约6-7分钟
                { min: 400, max: 450 } // 约7-8分钟
            ];

            // 随机选择一个等待时间范围
            const randomIndex = getRandomInt(0, waitRanges.length - 1);
            const selectedRange = waitRanges[randomIndex];

            // 计算实际等待时间(毫秒)
            const waitTime = randomInt(selectedRange.min * 1000, selectedRange.max * 1000);

            log(`已选择等待方案${randomIndex + 1}，等待${waitTime / 1000}秒(${(waitTime / 60000).toFixed(1)}分钟)后执行任务`);
            await $.wait(waitTime);
            console.log(
                `实际开始运行脚本时间：${dayjs().format("YYYY-MM-DD HH:mm:ss")}`
            );
            for (let index = 0; index < tpyqcArr.length; index++) {
                let num = index + 1
                log(`\n==== 开始【第 ${num} 个账号】====\n`)
                tpyqc = tpyqcArr[index].split('&');
                await login()

                if (index < tpyqcArr.length - 1) {
                    const waitTime = randomInt(20000, 30000);
                    console.log(`等待${waitTime / 1000}秒后运行下一个账号`);
                    await $.wait(waitTime);
                } else {
                    console.log(`已完成所有账号的任务${Notify===1? '，准备发送通知': ''}...}`);
                }
            }
            await SendMsg(msg);
        }
    }
})()
.catch((e) => log(e))
    .finally(() => $.done())

// 登录
async function login() {
    return new Promise((resolve) => {
        var options = {
            method: 'POST',
            url: `${hostname}/auto_passport3_back_intf/passport3/rest/login_new.jsp`,
            headers: {
                "Host": host,
                "Content-Type": 'application/x-www-form-urlencoded',
            },
            data: `password=${tpyqc[1]}&username=${tpyqc[0]}`
        };
        if (debug) {
            log(`\n【debug】=============== 这是  请求 url ===============`);
            log(JSON.stringify(options));
        }
        axios.request(options).then(async function(response) {
            try {
                if (debug) {
                    log(`\n\n【debug】===============这是 返回data==============`);
                    log(JSON.stringify(response.data));
                }
                const result = response.data;
                if (result.status == 0) {
                    ck = result.common_session_id;
                    console.log('cookie：', ck);
                    log(`\n 账号登录: ✅ ，${result.message}`)
                    await userInfo()
                    await $.wait(3000)
                    await getTask()
                    await $.wait(3000)
                    await userInfo(true)
                } else {
                    log(`\n 账号登录: ❌ ，原因是：${result.msg}`)
                }
            } catch (e) {
                log(`异常：${e}，原因：${e.msg}`)
            }
        }).catch(function(error) {
            console.error(error);
        }).then(res => {
            //这里处理正确返回
            resolve();
        });
    })
}

async function signIn() {
    return new Promise((resolve) => {
        var options = {
            method: 'GET',
            url: `https://api.pcauto.com.cn/user-growth/sign/signCenterInfo`,
            headers: {
                "Cookie": `common_session_id=${ck}`,
            },
            data: {}
        };
        if (debug) {
            log(`\n【debug】=============== 这是  请求 url ===============`);
            log(JSON.stringify(options));
        }
        axios.request(options).then(async function(response) {
            try {
                if (debug) {
                    log(`\n\n【debug】===============这是 返回data==============`);
                    log(JSON.stringify(response.data));
                }
                const data = response.data
                if (data.code === 200) {
                    console.log(data.message)
                } else {
                    log(data.msg)
                }

            } catch (e) {
                log(`异常：${e}，原因：${e.msg}`)
            }
        }).catch(function(error) {
            console.error(error);
        }).then(res => {
            //这里处理正确返回
            resolve();
        });
    })

}

async function userInfo(send = false) {
    return new Promise((resolve) => {
        var options = {
            method: 'GET',
            url: `https://api.pcauto.com.cn/user-growth/user/userInfo`,
            headers: {
                "Cookie": `common_session_id=${ck}`,
            },
            data: {}
        };
        if (debug) {
            log(`\n【debug】=============== 这是  请求 url ===============`);
            log(JSON.stringify(options));
        }
        axios.request(options).then(async function(response) {
            try {
                if (debug) {
                    log(`\n\n【debug】===============这是 返回data==============`);
                    log(JSON.stringify(response.data));
                }
                const data = response.data
                if (data.code === 200) {
                    if (data.data.signTip.includes('签到')) {
                        await signIn()
                    }
                    console.log(`当前积分：${data.data.point}`)
                    if (send) {
                        const phone = tpyqc[0].replace(
                            /(\d{3})\d{4}(\d{4})/,
                            "$1****$2"
                        );
                        msg += `\n【${phone}】 当前积分:   ${data.data.point}\n`;
                    }
                } else {
                    log(data.msg)
                }

            } catch (e) {
                log(`异常：${e}，原因：${e.msg}`)
            }
        }).catch(function(error) {
            console.error(error);
        }).then(res => {
            //这里处理正确返回
            resolve();
        });
    })

}

async function getTask() {
    return new Promise((resolve) => {
        var options = {
            method: 'GET',
            url: `https://api.pcauto.com.cn/user-growth/sign/getTaskInfo?sessionId=${ck}`,
            headers: {
                "Cookie": `common_session_id=${ck}`,
            },
            data: {}
        };
        if (debug) {
            log(`\n【debug】=============== 这是  请求 url ===============`);
            log(JSON.stringify(options));
        }
        axios.request(options).then(async function(response) {
            try {
                if (debug) {
                    log(`\n\n【debug】===============这是 返回data==============`);
                    log(JSON.stringify(response.data));
                }
                const data = response.data
                if (data.code === 200) {
                    await getClubs()
                    console.log(`\n开始处理每日任务列表...`)
                    const dailyTasks = data.data.dailyTaskList;
                    for (let i = 0; i < dailyTasks.length; i++) {
                        const task = dailyTasks[i];
                        console.log(`\n------- 任务${i+1}/${dailyTasks.length}: ${task.taskName} -------`)
                        try {
                            if (task.isFinish) {
                                console.log(`✅ ${task.taskName} 已完成，跳过处理`)
                                continue;
                            }
                            if (task.taskName.includes('发布')) {
                                console.log(`当前选择的是${issueFlag? '发布': '不发布'}任务}`)
                                if (issueFlag) {
                                    const content = connentArray[0].content || connentArray[0].appContent || connentArray[0].wapContent;
                                    await addIssue(content)
                                    console.log(`✅ 发布：${task.taskName} 已完成`)
                                }
                            }
                            // 处理点赞任务
                            if (task.taskName.includes('点赞')) {
                                await $.wait(1000)
                                console.log(`开始执行点赞，需要点赞${task.taskNum}次，已完成${task.finishTaskNum}次`)
                                for (let j = task.finishTaskNum; j < task.taskNum; j++) {
                                    console.log(`点赞进度: ${j+1}/${task.taskNum}`)
                                    const element = connentArray[j];
                                    if (!element || !element.issueIdStr) {
                                        console.log(`⚠️ 无法获取第${j+1}个内容的ID，跳过`)
                                        continue;
                                    }
                                    await like(element.issueIdStr)
                                    await $.wait(3000)
                                }
                                console.log(`✅ ${task.taskName} 已完成`)
                            }
                            // 处理分享任务
                            else if (task.taskName.includes('分享')) {
                                const shareList = task.shareList;
                                console.log(`开始执行分享，共${shareList.length}个分享任务`)
                                for (let j = 0; j < shareList.length; j++) {
                                    const shareItem = shareList[j];
                                    console.log(`分享任务${j+1}/${shareList.length}: ${shareItem.showName}`)

                                    if (!shareItem.isFinish) {
                                        // 确定正确的内容类型
                                        const contentType = j === 0 ? 'Post' : (shareItem.subType || 'Post');
                                        await doShareTask(shareItem.contentId, contentType)
                                        console.log(`✅ 分享：${shareItem.showName} 完成`)
                                    } else {
                                        console.log(`✅ 分享：${shareItem.showName} 已完成，跳过`)
                                    }

                                    if (j < shareList.length - 1) await $.wait(2000);
                                }
                            }
                        } catch (err) {
                            console.log(`❌ 处理任务"${task.taskName}"时出错: ${err}`)
                        }

                        // 任务间隔
                        if (i < dailyTasks.length - 1) await $.wait(3000)
                    }
                    console.log(`\n所有每日任务处理完毕，开始处理月度任务`);
                    await processMonthTasks();
                    console.log(`所有任务处理完毕`);
                } else {
                    log(data.msg)
                }

            } catch (e) {
                log(`异常：${e}，原因：${e.msg}`)
            }
        }).catch(function(error) {
            console.error(error);
        }).then(res => {
            //这里处理正确返回
            resolve();
        });
    })

}

async function getClubs() {
    return new Promise((resolve) => {
        var options = {
            method: 'GET',
            url: `https://community-gateway.pcauto.com.cn/app/club/allClubs?isNewPower=false`,
            headers: {
                "Cookie": `common_session_id=${ck}`,
            },
            data: {}
        };
        if (debug) {
            log(`\n【debug】=============== 这是  请求 url ===============`);
            log(JSON.stringify(options));
        }
        axios.request(options).then(async function(response) {
            try {
                if (debug) {
                    log(`\n\n【debug】===============这是 返回data==============`);
                    log(JSON.stringify(response.data));
                }
                const data = response.data
                if (data.code === 200) {
                    // 存储所有社区数据
                    const allClubs = data.data.allClubs;
                    let totalFlag = false;
                    let attempts = 0;
                    while (!totalFlag) {
                        // 每次循环重新随机选择一个社区
                        const random = Math.floor(Math.random() * allClubs.length);
                        console.log(`尝试第 ${attempts + 1} 次，随机获取社区：${allClubs[random].name}  ${allClubs[random].idStr}`);
                        totalFlag = await contentList(allClubs[random].idStr);
                        attempts++;
                        await $.wait(3000);
                    }
                    if (totalFlag) {
                        console.log("成功获取到内容列表");
                    }
                } else {
                    log(data.msg)
                }

            } catch (e) {
                log(`异常：${e}，原因：${e.msg}`)
            }
        }).catch(function(error) {
            console.error(error);
        }).then(res => {
            //这里处理正确返回
            resolve();
        });
    })

}

async function contentList(id) {
    let total = false
    return new Promise((resolve) => {
        var options = {
            method: 'GET',
            url: `https://community-gateway.pcauto.com.cn/app/tags/contentList?id=${id}&isSuperior=false&orderType=0&pageNo=1&pageSize=20&tagType=Club`,
            headers: {
                "Cookie": `common_session_id=${ck}`,
            },
            data: {}
        };
        if (debug) {
            log(`\n【debug】=============== 这是  请求 url ===============`);
            log(JSON.stringify(options));
        }
        axios.request(options).then(async function(response) {
            try {
                if (debug) {
                    log(`\n\n【debug】===============这是 返回data==============`);
                    log(JSON.stringify(response.data));
                }
                const data = response.data
                if (data.code === 200) {
                    connentArray = []
                    connentArray = data.data.data
                    if (data.data.total >= 20) {
                        total = true
                    }
                } else {
                    log(data.msg)
                }

            } catch (e) {
                log(`异常：${e}，原因：${e.msg}`)
            }
        }).catch(function(error) {
            console.error(error);
        }).then(res => {
            //这里处理正确返回
            resolve(total);
        });
    })

}

// 发帖
async function addIssue(content) {
    return new Promise((resolve) => {
        // 使用当前时间戳
        var options = {
            method: 'POST',
            url: `https://community-gateway.pcauto.com.cn/app/topic/issue`,
            headers: {
                "Cookie": `common_session_id=${ck}`,
            },
            data: { "accountAtDTOList": [], "clubTags": ["1"], "content": content, "themeTags": [], "title": "" }
        };
        if (debug) {
            log(`\n【debug】=============== 这是  请求 url ===============`);
            log(JSON.stringify(options));
        }
        axios.request(options).then(async function(response) {
            try {
                if (debug) {
                    log(`\n\n【debug】===============这是 返回data==============`);
                    log(JSON.stringify(response.data));
                }
                const data = response.data
                if (data.code === 200) {
                    console.log(`发帖：${data.data ? '发帖成功': data.msg}`);
                } else {
                    log(data.msg)
                }

            } catch (e) {
                log(`异常：${e}，原因：${e.msg}`)
            }
        }).catch(function(error) {
            console.error(error);
        }).then(res => {
            //这里处理正确返回
            resolve();
        });
    })

}

// 点赞
async function like(contentId) {
    return new Promise((resolve) => {
        // 使用当前时间戳
        var options = {
            method: 'POST',
            url: `https://community-gateway.pcauto.com.cn/app/social/addLike`,
            headers: {
                "Cookie": `common_session_id=${ck}`,
            },
            data: {
                "actionType": 1,
                "contentId": contentId,
                "contentType": "Post"
            }
        };
        if (debug) {
            log(`\n【debug】=============== 这是  请求 url ===============`);
            log(JSON.stringify(options));
        }
        axios.request(options).then(async function(response) {
            try {
                if (debug) {
                    log(`\n\n【debug】===============这是 返回data==============`);
                    log(JSON.stringify(response.data));
                }
                const data = response.data
                if (data.code === 200) {
                    console.log(`点赞：${data.data.errorMsg === null? '点赞成功': data.data.errorMsg}`);
                } else {
                    log(data.msg)
                }

            } catch (e) {
                log(`异常：${e}，原因：${e.msg}`)
            }
        }).catch(function(error) {
            console.error(error);
        }).then(res => {
            //这里处理正确返回
            resolve();
        });
    })

}

// 分享
async function doShareTask(contentId, contentType) {
    return new Promise((resolve) => {
        // 使用当前时间戳
        var options = {
            method: 'POST',
            url: `https://api.pcauto.com.cn/user-growth/task/doShareTask`,
            headers: {
                "Cookie": `common_session_id=${ck}`,
            },
            data: {
                "contentId": contentId,
                "contentType": contentType
            }
        };
        if (debug) {
            log(`\n【debug】=============== 这是  请求 url ===============`);
            log(JSON.stringify(options));
        }
        axios.request(options).then(async function(response) {
            try {
                if (debug) {
                    log(`\n\n【debug】===============这是 返回data==============`);
                    log(JSON.stringify(response.data));
                }
                const data = response.data
                if (data.status === 1) {
                    console.log(`分享：${data.errorMsg === null? '分享成功': data.errorMsg}`);
                } else {
                    log(data.msg)
                }

            } catch (e) {
                log(`异常：${e}，原因：${e.msg}`)
            }
        }).catch(function(error) {
            console.error(error);
        }).then(res => {
            //这里处理正确返回
            resolve();
        });
    })

}

// 生成指定范围内的随机整数
function getRandomInt(min, max) {
    min = Math.ceil(min);
    max = Math.floor(max);
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 生成指定范围内的随机数(包括小数)
function randomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

async function SendMsg(message) {
    if (!message) return;

    if (Notify > 0) {
        // 注释掉或移除引用外部模块的部分
        /* if ($.isNode()) {
            var notify = require("./sendNotify");
            await notify.sendNotify($.name, message);
        } else { */
            // 直接使用内部消息方法或控制台输出
            log(message);
        // }
    } else {
        log(message);
    }
}


async function Envs() {
    if (tpyqc) {
        if (tpyqc.indexOf("@") != -1) {
            tpyqc.split("@").forEach((item) => {

                tpyqcArr.push(item);
            });
        } else if (tpyqc.indexOf("\n") != -1) {
            tpyqc.split("\n").forEach((item) => {
                tpyqcArr.push(item);
            });
        } else {
            tpyqcArr.push(tpyqc);
        }
    } else {
        log(`\n 【${$.name}】：未填写变量 tpyqc`)
        return;
    }

    return true;
}

function Env(t, e) {
    "undefined" != typeof process && JSON.stringify(process.env).indexOf("GITHUB") > -1 && process.exit(0);

    class s {
        constructor(t) {
            this.env = t
        }

        send(t, e = "GET") {
            t = "string" == typeof t ? {
                url: t
            } : t;
            let s = this.get;
            return "POST" === e && (s = this.post), new Promise((e, i) => {
                s.call(this, t, (t, s, r) => {
                    t ? i(t) : e(s)
                })
            })
        }

        get(t) {
            return this.send.call(this.env, t)
        }

        post(t) {
            return this.send.call(this.env, t, "POST")
        }
    }

    return new class {
        constructor(t, e) {
            this.name = t, this.http = new s(this), this.data = null, this.dataFile = "box.dat", this.logs = [], this.isMute = !1, this.isNeedRewrite = !1, this.logSeparator = "\n", this.startTime = (new Date).getTime(), Object.assign(this, e), this.log("", `🔔${this.name}, 开始!`)
        }

        isNode() {
            return "undefined" != typeof module && !!module.exports
        }

        isQuanX() {
            return "undefined" != typeof $task
        }

        isSurge() {
            return "undefined" != typeof $httpClient && "undefined" == typeof $loon
        }

        isLoon() {
            return "undefined" != typeof $loon
        }

        toObj(t, e = null) {
            try {
                return JSON.parse(t)
            } catch {
                return e
            }
        }

        toStr(t, e = null) {
            try {
                return JSON.stringify(t)
            } catch {
                return e
            }
        }

        getjson(t, e) {
            let s = e;
            const i = this.getdata(t);
            if (i) try {
                s = JSON.parse(this.getdata(t))
            } catch {}
            return s
        }

        setjson(t, e) {
            try {
                return this.setdata(JSON.stringify(t), e)
            } catch {
                return !1
            }
        }

        getScript(t) {
            return new Promise(e => {
                this.get({
                    url: t
                }, (t, s, i) => e(i))
            })
        }

        runScript(t, e) {
            return new Promise(s => {
                let i = this.getdata("@chavy_boxjs_userCfgs.httpapi");
                i = i ? i.replace(/\n/g, "").trim() : i;
                let r = this.getdata("@chavy_boxjs_userCfgs.httpapi_timeout");
                r = r ? 1 * r : 20, r = e && e.timeout ? e.timeout : r;
                const [o, h] = i.split("@"), n = {
                    url: `http://${h}/v1/scripting/evaluate`,
                    body: {
                        script_text: t,
                        mock_type: "cron",
                        timeout: r
                    },
                    headers: {
                        "X-Key": o,
                        Accept: "*/*"
                    }
                };
                this.post(n, (t, e, i) => s(i))
            }).catch(t => this.logErr(t))
        }

        loaddata() {
            if (!this.isNode()) return {}; {
                this.fs = this.fs ? this.fs : require("fs"), this.path = this.path ? this.path : require("path");
                const t = this.path.resolve(this.dataFile),
                    e = this.path.resolve(process.cwd(), this.dataFile),
                    s = this.fs.existsSync(t),
                    i = !s && this.fs.existsSync(e);
                if (!s && !i) return {}; {
                    const i = s ? t : e;
                    try {
                        return JSON.parse(this.fs.readFileSync(i))
                    } catch (t) {
                        return {}
                    }
                }
            }
        }

        writedata() {
            if (this.isNode()) {
                this.fs = this.fs ? this.fs : require("fs"), this.path = this.path ? this.path : require("path");
                const t = this.path.resolve(this.dataFile),
                    e = this.path.resolve(process.cwd(), this.dataFile),
                    s = this.fs.existsSync(t),
                    i = !s && this.fs.existsSync(e),
                    r = JSON.stringify(this.data);
                s ? this.fs.writeFileSync(t, r) : i ? this.fs.writeFileSync(e, r) : this.fs.writeFileSync(t, r)
            }
        }

        lodash_get(t, e, s) {
            const i = e.replace(/\[(\d+)\]/g, ".$1").split(".");
            let r = t;
            for (const t of i)
                if (r = Object(r)[t], void 0 === r) return s;
            return r
        }

        lodash_set(t, e, s) {
            return Object(t) !== t ? t : (Array.isArray(e) || (e = e.toString().match(/[^.[\]]+/g) || []), e.slice(0, -1).reduce((t, s, i) => Object(t[s]) === t[s] ? t[s] : t[s] = Math.abs(e[i + 1]) >> 0 == +e[i + 1] ? [] : {}, t)[e[e.length - 1]] = s, t)
        }

        getdata(t) {
            let e = this.getval(t);
            if (/^@/.test(t)) {
                const [, s, i] = /^@(.*?)\.(.*?)$/.exec(t), r = s ? this.getval(s) : "";
                if (r) try {
                    const t = JSON.parse(r);
                    e = t ? this.lodash_get(t, i, "") : e
                } catch (t) {
                    e = ""
                }
            }
            return e
        }

        setdata(t, e) {
            let s = !1;
            if (/^@/.test(e)) {
                const [, i, r] = /^@(.*?)\.(.*?)$/.exec(e), o = this.getval(i),
                    h = i ? "null" === o ? null : o || "{}" : "{}";
                try {
                    const e = JSON.parse(h);
                    this.lodash_set(e, r, t), s = this.setval(JSON.stringify(e), i)
                } catch (e) {
                    const o = {};
                    this.lodash_set(o, r, t), s = this.setval(JSON.stringify(o), i)
                }
            } else s = this.setval(t, e);
            return s
        }

        getval(t) {
            return this.isSurge() || this.isLoon() ? $persistentStore.read(t) : this.isQuanX() ? $prefs.valueForKey(t) : this.isNode() ? (this.data = this.loaddata(), this.data[t]) : this.data && this.data[t] || null
        }

        setval(t, e) {
            return this.isSurge() || this.isLoon() ? $persistentStore.write(t, e) : this.isQuanX() ? $prefs.setValueForKey(t, e) : this.isNode() ? (this.data = this.loaddata(), this.data[e] = t, this.writedata(), !0) : this.data && this.data[e] || null
        }

        initGotEnv(t) {
            this.got = this.got ? this.got : require("got"), this.cktough = this.cktough ? this.cktough : require("tough-cookie"), this.ckjar = this.ckjar ? this.ckjar : new this.cktough.CookieJar, t && (t.headers = t.headers ? t.headers : {}, void 0 === t.headers.Cookie && void 0 === t.cookieJar && (t.cookieJar = this.ckjar))
        }

        get(t, e = (() => {})) {
            t.headers && (delete t.headers["Content-Type"], delete t.headers["Content-Length"]), this.isSurge() || this.isLoon() ? (this.isSurge() && this.isNeedRewrite && (t.headers = t.headers || {}, Object.assign(t.headers, {
                "X-Surge-Skip-Scripting": !1
            })), $httpClient.get(t, (t, s, i) => {
                !t && s && (s.body = i, s.statusCode = s.status), e(t, s, i)
            })) : this.isQuanX() ? (this.isNeedRewrite && (t.opts = t.opts || {}, Object.assign(t.opts, {
                hints: !1
            })), $task.fetch(t).then(t => {
                const {
                    statusCode: s,
                    statusCode: i,
                    headers: r,
                    body: o
                } = t;
                e(null, {
                    status: s,
                    statusCode: i,
                    headers: r,
                    body: o
                }, o)
            }, t => e(t))) : this.isNode() && (this.initGotEnv(t), this.got(t).on("redirect", (t, e) => {
                try {
                    if (t.headers["set-cookie"]) {
                        const s = t.headers["set-cookie"].map(this.cktough.Cookie.parse).toString();
                        s && this.ckjar.setCookieSync(s, null), e.cookieJar = this.ckjar
                    }
                } catch (t) {
                    this.logErr(t)
                }
            }).then(t => {
                const {
                    statusCode: s,
                    statusCode: i,
                    headers: r,
                    body: o
                } = t;
                e(null, {
                    status: s,
                    statusCode: i,
                    headers: r,
                    body: o
                }, o)
            }, t => {
                const {
                    message: s,
                    response: i
                } = t;
                e(s, i, i && i.body)
            }))
        }

        post(t, e = (() => {})) {
            if (t.body && t.headers && !t.headers["Content-Type"] && (t.headers["Content-Type"] = "application/x-www-form-urlencoded"), t.headers && delete t.headers["Content-Length"], this.isSurge() || this.isLoon()) this.isSurge() && this.isNeedRewrite && (t.headers = t.headers || {}, Object.assign(t.headers, {
                "X-Surge-Skip-Scripting": !1
            })), $httpClient.post(t, (t, s, i) => {
                !t && s && (s.body = i, s.statusCode = s.status), e(t, s, i)
            });
            else if (this.isQuanX()) t.method = "POST", this.isNeedRewrite && (t.opts = t.opts || {}, Object.assign(t.opts, {
                hints: !1
            })), $task.fetch(t).then(t => {
                const {
                    statusCode: s,
                    statusCode: i,
                    headers: r,
                    body: o
                } = t;
                e(null, {
                    status: s,
                    statusCode: i,
                    headers: r,
                    body: o
                }, o)
            }, t => e(t));
            else if (this.isNode()) {
                this.initGotEnv(t);
                const {
                    url: s,
                    ...i
                } = t;
                this.got.post(s, i).then(t => {
                    const {
                        statusCode: s,
                        statusCode: i,
                        headers: r,
                        body: o
                    } = t;
                    e(null, {
                        status: s,
                        statusCode: i,
                        headers: r,
                        body: o
                    }, o)
                }, t => {
                    const {
                        message: s,
                        response: i
                    } = t;
                    e(s, i, i && i.body)
                })
            }
        }

        time(t, e = null) {
            const s = e ? new Date(e) : new Date;
            let i = {
                "M+": s.getMonth() + 1,
                "d+": s.getDate(),
                "H+": s.getHours(),
                "m+": s.getMinutes(),
                "s+": s.getSeconds(),
                "q+": Math.floor((s.getMonth() + 3) / 3),
                S: s.getMilliseconds()
            };
            /(y+)/.test(t) && (t = t.replace(RegExp.$1, (s.getFullYear() + "").substr(4 - RegExp.$1.length)));
            for (let e in i) new RegExp("(" + e + ")").test(t) && (t = t.replace(RegExp.$1, 1 == RegExp.$1.length ? i[e] : ("00" + i[e]).substr(("" + i[e]).length)));
            return t
        }

        msg(e = t, s = "", i = "", r) {
            const o = t => {
                if (!t) return t;
                if ("string" == typeof t) return this.isLoon() ? t : this.isQuanX() ? {
                    "open-url": t
                } : this.isSurge() ? {
                    url: t
                } : void 0;
                if ("object" == typeof t) {
                    if (this.isLoon()) {
                        let e = t.openUrl || t.url || t["open-url"],
                            s = t.mediaUrl || t["media-url"];
                        return {
                            openUrl: e,
                            mediaUrl: s
                        }
                    }
                    if (this.isQuanX()) {
                        let e = t["open-url"] || t.url || t.openUrl,
                            s = t["media-url"] || t.mediaUrl;
                        return {
                            "open-url": e,
                            "media-url": s
                        }
                    }
                    if (this.isSurge()) {
                        let e = t.url || t.openUrl || t["open-url"];
                        return {
                            url: e
                        }
                    }
                }
            };
            if (this.isMute || (this.isSurge() || this.isLoon() ? $notification.post(e, s, i, o(r)) : this.isQuanX() && $notify(e, s, i, o(r))), !this.isMuteLog) {
                let t = ["", "==============📣系统通知📣=============="];
                t.push(e), s && t.push(s), i && t.push(i), console.log(t.join("\n")), this.logs = this.logs.concat(t)
            }
        }

        log(...t) {
            t.length > 0 && (this.logs = [...this.logs, ...t]), console.log(t.join(this.logSeparator))
        }

        logErr(t, e) {
            const s = !this.isSurge() && !this.isQuanX() && !this.isLoon();
            s ? this.log("", `❗️${this.name}, 错误!`, t.stack) : this.log("", `❗️${this.name}, 错误!`, t)
        }

        wait(t) {
            return new Promise(e => setTimeout(e, t))
        }

        done(t = {}) {
            const e = (new Date).getTime(),
                s = (e - this.startTime) / 1e3;
            this.log("", `🔔${this.name}, 结束! 🕛 ${s} 秒`), this.log(), (this.isSurge() || this.isQuanX() || this.isLoon()) && $done(t)
        }
    }(t, e)
}

async function processMonthTasks() {
    return new Promise((resolve) => {
        var options = {
            method: 'GET',
            url: `https://api.pcauto.com.cn/user-growth/sign/getTaskInfo?sessionId=${ck}`,
            headers: {
                "Cookie": `common_session_id=${ck}`,
                "User-Agent": "Mozilla/5.0 (Linux; Android 15; 24031PN0DC Build/AQ3A.240627.003; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/132.0.6834.163 Mobile Safari/537.36/PCAutoApp",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "sec-ch-ua-platform": "\"Android\"",
                "sec-ch-ua": "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Android WebView\";v=\"132\"",
                "sec-ch-ua-mobile": "?1",
                "sessionid": ck,
                "origin": "https://my.pcauto.com.cn",
                "x-requested-with": "cn.com.pcauto.android.browser",
                "sec-fetch-site": "same-site",
                "sec-fetch-mode": "cors",
                "sec-fetch-dest": "empty",
                "referer": "https://my.pcauto.com.cn/app/sign-in-center?common-bridge-web=1&hidden=1&statusBarStyle=1&needLogin=1&appVersion=8.2.1&safeHeight=51&defaultNavBarHeight=40",
                "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7"
            }
        };
        if (debug) {
            log(`\n【debug】=============== 这是  请求 url ===============`);
            log(JSON.stringify(options));
        }
        axios.request(options).then(async function(response) {
            try {
                if (debug) {
                    log(`\n\n【debug】===============这是 返回data==============`);
                    log(JSON.stringify(response.data));
                }
                const data = response.data;
                if (data.code === 200) {
                    console.log(`\n开始处理月度任务...`);
                    const monthTasks = data.data.monthTaskList || [];
                    if (monthTasks.length === 0) {
                        console.log(`没有月度任务可处理`);
                        resolve();
                        return;
                    }
                    
                    let receivedCount = 0;
                    
                    // 遍历月度任务类别
                    for (let i = 0; i < monthTasks.length; i++) {
                        const taskCategory = monthTasks[i];
                        console.log(`\n------- 月度任务类别: ${taskCategory.taskName} -------`);
                        console.log(`进度: ${taskCategory.finishTaskNum}/${taskCategory.taskNum}`);
                        
                        // 遍历任务项
                        const items = taskCategory.items || [];
                        for (let j = 0; j < items.length; j++) {
                            const item = items[j];
                            const status = item.isFinish 
                                ? (item.received === 1 ? "已完成已领取" : "已完成未领取") 
                                : "未完成";
                            
                            console.log(`任务项: ${item.itemName}, 积分: ${item.point}, 状态: ${status}`);
                            
                            // 只处理已完成但未领取的任务
                            if (item.isFinish && item.received === 0 && item.recordId) {
                                console.log(`准备领取任务: ${taskCategory.taskName} - ${item.itemName}`);
                                
                                try {
                                    const result = await receiveMonthTaskReward(item.recordId);
                                    if (result && result.code === 200) {
                                        console.log(`✅ 成功领取月度任务奖励: ${taskCategory.taskName} - ${item.itemName}，获得积分: ${item.point}`);
                                        receivedCount++;
                                    } else {
                                        const errMsg = result ? result.message : "请求失败";
                                        console.log(`❌ 领取月度任务奖励失败: ${errMsg}`);
                                    }
                                } catch (error) {
                                    console.log(`❌ 领取月度任务奖励出错: ${error}`);
                                }
                                
                                // 添加延迟，避免请求过快
                                await $.wait(2000);
                            }
                        }
                        
                        // 类别之间添加间隔
                        if (i < monthTasks.length - 1) await $.wait(1000);
                    }
                    
                    console.log(`\n月度任务处理完成，成功领取 ${receivedCount} 个奖励`);
                } else {
                    log(`获取月度任务信息失败: ${data.message}`);
                }
            } catch (e) {
                log(`处理月度任务异常：${e}`);
            }
            resolve();
        }).catch(function(error) {
            console.error(`处理月度任务请求错误: ${error}`);
            resolve();
        });
    });
}

// 领取月度任务奖励
async function receiveMonthTaskReward(recordId) {
    return new Promise((resolve) => {
        var options = {
            method: 'POST',
            url: `https://api.pcauto.com.cn/user-growth/sign/received`,
            headers: {
                "User-Agent": "Mozilla/5.0 (Linux; Android 15; 24031PN0DC Build/AQ3A.240627.003; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/132.0.6834.163 Mobile Safari/537.36/PCAutoApp",
                "Accept": "application/json",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "Content-Type": "application/json",
                "sec-ch-ua-platform": "\"Android\"",
                "sec-ch-ua": "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Android WebView\";v=\"132\"",
                "sec-ch-ua-mobile": "?1",
                "sessionid": ck,
                "origin": "https://my.pcauto.com.cn",
                "x-requested-with": "cn.com.pcauto.android.browser",
                "sec-fetch-site": "same-site",
                "sec-fetch-mode": "cors",
                "sec-fetch-dest": "empty",
                "referer": "https://my.pcauto.com.cn/app/sign-in-center?common-bridge-web=1&hidden=1&statusBarStyle=1&needLogin=1&appVersion=8.2.1&safeHeight=51&defaultNavBarHeight=40",
                "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
                "Cookie": `common_session_id=${ck}`
            },
            data: {
                "recordId": recordId,
                "sessionId": ck
            }
        };
        
        if (debug) {
            log(`\n【debug】=============== 这是领取月度任务奖励请求 ===============`);
            log(JSON.stringify(options));
        }
        
        axios.request(options).then(function(response) {
            try {
                if (debug) {
                    log(`\n\n【debug】===============这是领取月度任务奖励返回data==============`);
                    log(JSON.stringify(response.data));
                }
                resolve(response.data);
            } catch (e) {
                log(`领取月度任务奖励异常：${e}`);
                resolve(null);
            }
        }).catch(function(error) {
            console.error(`领取月度任务奖励请求错误: ${error}`);
            resolve(null);
        });
    });
}