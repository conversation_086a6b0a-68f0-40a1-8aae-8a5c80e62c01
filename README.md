# 联通查询系统 v2.1

一个现代化的联通积分查询系统，具有严格的安全控制、后端独立自动查询和完整的部署自动化功能。

## ✨ 主要特性

### 🔐 严格的安全控制
- **系统级别单次注册**：只允许注册一次，无法绕过，跨浏览器生效
- **多重安全防护**：前端+后端+数据库三层验证机制
- **防并发注册**：使用数据库锁机制防止同时注册
- **长期登录**：登录有效期365天，减少重复登录操作
- **密码加密**：使用SHA256哈希加密存储用户密码

### 🔄 后端独立自动查询
- **完全独立运行**：不依赖前端页面是否打开，后端独立定时执行
- **智能定时调度**：默认每小时自动查询一次，可灵活配置
- **状态实时同步**：前端可实时查看后端查询状态和进度
- **无密码查询**：移除查询密码验证，简化操作流程
- **手动即时查询**：随时可以手动触发查询操作

### 🗄️ 数据管理
- **SQLite数据库**：查询结果持久化存储在本地数据库
- **数据一致性**：页面数据从数据库读取，确保数据准确性
- **历史记录保存**：完整保存所有查询历史，支持数据追溯

### 🎨 现代化界面
- **专业图标**：联通主题的SVG网站图标，提升品牌形象
- **现代化设计**：美观的卡片式布局和动画效果
- **响应式设计**：完美适配桌面端和移动端设备
- **主题切换**：支持深色/浅色主题无缝切换
- **实时统计**：动态显示账户统计和查询状态

### 🚀 完整的部署自动化
- **一键部署脚本**：自动检查环境、构建容器、启动服务
- **部署验证**：自动验证所有功能是否正常工作
- **Docker集成**：完整的Docker和Docker Compose配置
- **健康检查**：容器健康监控和自动重启

## 🚀 快速部署

### 一键部署（推荐）

```bash
# Linux/macOS
chmod +x deploy.sh
./deploy.sh

# Windows (Git Bash)
bash deploy.sh
```

### Docker Compose 部署

```bash
docker-compose up -d --build
```

### 手动部署

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动服务
python server.py

# 3. 访问系统
# http://localhost:2032
```

## 🔑 首次使用

1. **访问系统**：打开 http://localhost:2032
2. **注册账号**：点击"注册"选项卡，输入用户名和密码
3. **登录系统**：使用注册的账号登录（有效期365天）
4. **开始查询**：登录后可直接进行查询，无需输入查询密码

> **注意**：系统只允许注册一次，注册成功后注册按钮会自动隐藏

## 📁 项目结构

```
联通查询系统/
├── server.py              # 主服务器文件
├── database.py            # 数据库操作模块
├── ltcx.py               # 联通查询核心模块
├── index.html            # 前端用户界面
├── Dockerfile            # Docker构建配置
├── docker-compose.yml    # Docker Compose配置
├── favicon.svg           # 网站图标
├── timezone_config.py    # 时区配置模块
├── deploy.sh             # 一键部署脚本
├── verify_deployment.sh  # 部署验证脚本
├── requirements.txt      # Python依赖列表
├── query_results.db      # SQLite数据库（运行时生成）
└── README.md             # 项目说明文档
```

## 🛠️ 系统要求

- **Docker方式**：Docker 20.10+ 和 Docker Compose（可选）
- **直接部署**：Python 3.8+ 和 pip
- **操作系统**：Linux、macOS、Windows

## 🔧 常用命令

### Docker 方式
```bash
# 查看服务状态
docker ps

# 查看日志
docker logs unicom-query

# 停止服务
./stop.sh

# 重启服务
./start.sh
```

### 直接部署方式
```bash
# 查看进程
ps aux | grep server.py

# 查看日志
tail -f server.log

# 停止服务
pkill -f "python.*server.py"
```

## 📊 技术栈

- **后端**：Python 3.8+ + HTTP Server + SQLite
- **前端**：HTML5 + CSS3 + JavaScript (ES6+)
- **数据库**：SQLite 3
- **部署**：Docker + Docker Compose + Shell Script
- **安全**：SHA256密码哈希 + 服务器端验证

## 🔍 故障排除

详细的故障排除指南请参考 [DEPLOYMENT.md](DEPLOYMENT.md) 文档。

## 📝 更新日志

### v2.1 (2025-06-20)
- ✅ 实现严格的单次注册限制（跨浏览器生效）
- ✅ 后端独立自动查询系统（不依赖前端）
- ✅ 添加专业的联通主题网站图标
- ✅ 完善Docker部署配置和自动化
- ✅ 添加部署验证脚本
- ✅ 优化时区配置和显示
- ✅ 强化安全防护机制

### v2.0 (2025-06-16)
- ✅ 新增用户注册登录系统
- ✅ 集成SQLite数据库存储
- ✅ 移除查询密码验证
- ✅ 延长登录有效期至365天
- ✅ 优化用户界面和交互体验
- ✅ 完善部署脚本和文档

### v1.0
- ✅ 基础查询功能
- ✅ 自动定时查询
- ✅ 响应式UI设计
- ✅ Docker部署支持

## 📞 技术支持

如遇到问题，请：
1. 查看 [DEPLOYMENT.md](DEPLOYMENT.md) 详细文档
2. 检查系统日志和错误信息
3. 确认网络连接和端口状态
4. 验证依赖安装情况

---

**开发信息**
- 版本：v2.1
- 更新：2025-06-20
- 许可：MIT License