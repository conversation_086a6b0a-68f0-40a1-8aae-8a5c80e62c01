import requests
import json
from datetime import datetime

# 多账号cookie配置
ACCOUNTS = {
    "主账号": "passToken=V1:DXmurwq2/R1BHTELu6obCesS3kzcLLWi0s7UW8UfGKmiQoGMffU9KleB8JDhA+xp6Y+zP2exneIit4U52YeSAEsNNi+Het+IFsXW8QCXUOPxyNkqYZ/KZB1/JTVX4M0wEzKa7LQsvQXz5fNS3pTHPcQ+J1v6AjRzBRHxHW9KVRI4YKN3d7Zg+tg95Kztxo2c/DM2XOdqZK6R5lTbJ/AEE+UxzdNjIuRvJoglhW3EST/7BGDBwiJpmQLXHAVXyLJ3dypSb4yWQCjSLPBIQxfA6Klb0qM/1kv7vK0tEFEi/43MxTeB4/5shMU8vVfQcxOI3pzZgy00OBBSQRwdAEDu8g==; userId=**********;",
    "账号2": "passToken=V1:CMlJzViwLip2ZUSLOH7il9zY4mLCv2mmZQh3baQy3/grzaOoq2vtgbt0Df6w/o+RdKH3jhSXuFL8nalndfvm6unjvWCvncx0Xu5kzwliQmk/coWc5IJryAoO5bYAl5zpRdsuLBXj4JkBYL6AzZo9nt33Hzn1MS+IZXTvG8VtLDhjamso6BVlcxhwxWRugOKIupHAaFIORIZW0oG9uV5FW7LvERQzOn+g2CnlP0vVENzYOPFctYvo+gpitDEMDdUEiQ2rXcaIdmBwRXHI6mNPJowU05jDNKa2VgrOgNMr3/eaHKM4ED531kSbY/HjTjX1GkHDYNHAE8Q89jB2MND3Og==; userId=**********;",
    "账号3": "passToken=V1:J7rrshrufaw8uWrlTMO7x5CiX0ytq3srsFMVrLcrFYrGw0oy5/yJX/+kkmEOpxKHoXWik69xuuyjneN5uFq5aEp7LVte5EaGvX3D80adQg+OSDfm/UJpXmIpFQmwHok7P+i9MUFgJa2bL9AlVzVoH/DkBZD6A8ghA2Ptg7z9zqFN3Nck+QdDybHKzsnQquIJ+sZ4B+0C2HxcApgvkefuiTqahDVZyx5U7+TdKTKw4eu9Z2gwBbnGOIreP//P5c1bCl6GS1Mq1DrV6ujg2HwbMa87fDAuUAA0+mkW/dUFH2TSYvwoJRY0m8mf2H3G9/yrkUV3X81vyjnvdLTbShubmQ==; userId=**********;",
    # 添加更多账号，格式：
    # "账号名": "passToken=完整token; userId=用户ID;",
}

def query_account(account_name, cookie):
    """查询单个账号的金币余额"""
    print(f"🔍 查询账号: {account_name}")

    session = requests.session()

    # 第一步：登录认证
    url = 'https://account.xiaomi.com/pass/serviceLogin?callback=https%3A%2F%2Fapi.jr.airstarfinance.net%2Fsts%3Fsign%3D1dbHuyAmee0NAZ2xsRw5vhdVQQ8%253D%26followup%3Dhttps%253A%252F%252Fm.jr.airstarfinance.net%252Fmp%252Fapi%252Flogin%253Ffrom%253Dmipay_indexicon_TVcard%2526deepLinkEnable%253Dfalse%2526requestUrl%253Dhttps%25253A%25252F%25252Fm.jr.airstarfinance.net%25252Fmp%25252Factivity%25252FvideoActivity%25253Ffrom%25253Dmipay_indexicon_TVcard%252526_noDarkMode%25253Dtrue%252526_transparentNaviBar%25253Dtrue%252526cUserId%25253Dusyxgr5xjumiQLUoAKTOgvi858Q%252526_statusBarHeight%25253D137&sid=jrairstar&_group=DEFAULT&_snsNone=true&_loginType=ticket'
    headers = {
        'user-agent': 'Mozilla/5.0 (Linux; U; Android 14; zh-CN; M2011K2C Build/UKQ1.240624.001; AppBundle/com.mipay.wallet; AppVersionName/6.87.0.5249.2312; AppVersionCode/********; MiuiVersion/stable-OS2.0.2.0.UKBCNXM; DeviceId/venus; NetworkType/MOBILE; mix_version) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3',
        'cookie': cookie
    }
    login_response = session.get(url=url, headers=headers)

    # 第二步：查询金币余额
    url = 'https://m.jr.airstarfinance.net/mp/api/generalActivity/queryUserGoldRichSum?app=com.mipay.wallet&deviceType=2&system=1&visitEnvironment=2&userExtra={"platformType":1,"com.miui.player":"********","com.miui.video":"v2024090290(MiVideo-UN)","com.mipay.wallet":"6.83.0.5175.2256"}&activityCode=2211-videoWelfare'
    headers = {
        'user-agent': 'Mozilla/5.0 (Linux; U; Android 14; zh-CN; M2011K2C Build/UKQ1.240624.001; AppBundle/com.mipay.wallet; AppVersionName/6.87.0.5249.2312; AppVersionCode/********; MiuiVersion/stable-OS2.0.2.0.UKBCNXM; DeviceId/venus; NetworkType/MOBILE; mix_version) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3',
    }

    response = session.get(url=url, headers=headers)

    # 解析响应
    try:
        data = json.loads(response.text)
        if data.get('success'):
            balance = data.get('value', 0)
            print(f"✅ {account_name}: {balance} 金币")

            # 显示详细响应（可选）
            print(f"   📄 完整响应: {response.text}")
            print(f"   📊 格式化JSON: {json.dumps(data, indent=2, ensure_ascii=False)}")

            return balance
        else:
            print(f"❌ {account_name}: 查询失败 - {data.get('error', '未知错误')}")
            return 0
    except json.JSONDecodeError:
        print(f"❌ {account_name}: 响应格式错误")
        print(f"   📄 原始响应: {response.text}")
        return 0

def main():
    """主程序 - 自动查询所有已配置的账号"""
    print(f"=== 小米金融多账号自动查询工具 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===")

    # 获取所有已配置的账号（跳过包含xxx的占位符）
    valid_accounts = [(name, cookie) for name, cookie in ACCOUNTS.items() if "xxx" not in cookie]

    if not valid_accounts:
        print("❌ 错误: 没有找到已配置的账号")
        print("请在ACCOUNTS字典中配置有效的cookie信息")
        return

    print(f"📋 发现 {len(valid_accounts)} 个已配置账号，开始自动查询...")

    total_balance = 0
    success_count = 0

    # 自动查询所有账号
    for i, (name, cookie) in enumerate(valid_accounts, 1):
        print(f"\n🔄 [{i}/{len(valid_accounts)}] 正在处理账号...")
        try:
            balance = query_account(name, cookie)
            if balance > 0:
                total_balance += balance
                success_count += 1
        except Exception as e:
            print(f"❌ 账号 {name} 查询失败: {str(e)}")

    # 输出汇总结果
    print(f"\n{'='*60}")
    print(f"📊 查询汇总报告")
    print(f"{'='*60}")
    print(f"总账号数: {len(valid_accounts)}")
    print(f"成功查询: {success_count}")
    print(f"失败账号: {len(valid_accounts) - success_count}")
    print(f"总金币余额: {total_balance} 金币")
    print(f"查询时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}")

    # 如果有失败的账号，提供建议
    if success_count < len(valid_accounts):
        print("\n💡 提示: 如有账号查询失败，请检查cookie是否过期或网络连接")

if __name__ == '__main__':
    main()
