// 泛微表单翻页优化精简版
(function() {
    const parseNumber = (value) => {
        if (!value) return null;
        const matches = String(value).match(/[+-]?([0-9,]*\.?[0-9]+)/);
        return matches ? Number(matches[0].replace(/,/g, '')) : null;
    };

    const setFieldColor = (field) => {
        const input = field.querySelector("input, span") || field;
        field.style.backgroundColor = parseNumber(input.textContent || input.value) > 0 ? "red" : "";
    };

    const boundFields = new Set();
    let reinitTimeout;

    const initFields = () => {
        // 处理所有目标字段
        document.querySelectorAll('[id="zjfd"], [id="yin1"]').forEach(setFieldColor);

        // WeFormSDK事件绑定
        if (window.WeFormSDK) {
            const sdk = window.WeFormSDK.getWeFormInstance();
            ['mxb1', 'mxb2'].forEach(table => {
                const fieldMark = sdk.convertFieldNameToId('zjfd', sdk.convertFieldNameToId(table));
                if (!boundFields.has(fieldMark)) {
                    sdk.bindFieldChangeEvent(fieldMark, (data) => 
                        document.querySelectorAll(`[fieldid^="${data.id.slice(5)}"]`).forEach(setFieldColor)
                    );
                    boundFields.add(fieldMark);
                }
            });
        }
    };

    // 防抖初始化
    const debouncedInit = () => {
        clearTimeout(reinitTimeout);
        reinitTimeout = setTimeout(initFields, 100);
    };

    // 事件委托
    ['input', 'change'].forEach(type => 
        document.addEventListener(type, (e) => {
            const field = e.target.closest('[id="zjfd"], [id="yin1"]');
            if (field) setFieldColor(field);
        }, true)
    );

    // DOM变化监听
    new MutationObserver(mutations => {
        if (mutations.some(m => [...m.addedNodes].some(n => 
            n.nodeType === 1 && (
                n.querySelector?.('[id="zjfd"], [id="yin1"]') ||
                ['zjfd', 'yin1', 'tr', 'tbody'].includes(n.id || n.tagName?.toLowerCase())
            )
        ))) debouncedInit();
    }).observe(document.body, { childList: true, subtree: true });

    // 翻页监听
    document.addEventListener('click', (e) => {
        if (e.target.closest('.ant-pagination-item, .ant-pagination-prev, .ant-pagination-next')) {
            setTimeout(initFields, 500);
        }
    }, true);

    // 泛微事件
    if (window.pageSdk) {
        ['tableRefresh', 'dataLoaded', 'formReady'].forEach(event => 
            pageSdk.on(event, () => setTimeout(initFields, 300))
        );
    }

    // jQuery Ajax监听
    window.$ && $(document).ajaxComplete((e, xhr, settings) => {
        settings.url?.match(/table|page|list/) && setTimeout(initFields, 300);
    });

    // 初始化
    if (!window.pageSdk) {
        document.readyState === 'loading' 
            ? document.addEventListener('DOMContentLoaded', initFields)
            : setTimeout(initFields, 1000);
    }
    
    // 保险措施
    window.addEventListener('load', () => setTimeout(initFields, 2000));

    // 调试接口
    window.weformCompact = { initFields, boundFields };
})();