#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一时区配置模块

提供统一的时区设置和日志格式化功能，确保所有时间显示都使用上海时区。
避免在多个文件中重复配置时区代码。

作者: 联通查询系统
创建时间: 2025-06-17
"""

import os
import time
import logging
from datetime import datetime, timezone, timedelta


def setup_timezone():
    """
    统一设置上海时区

    设置系统环境变量TZ为Asia/Shanghai，并调用tzset()使设置生效。
    这个函数应该在所有Python模块的开头调用。

    Returns:
        bool: 设置成功返回True，失败返回False
    """
    try:
        # 设置时区环境变量
        os.environ['TZ'] = 'Asia/Shanghai'

        # 如果系统支持tzset，调用它使时区设置生效
        if hasattr(time, 'tzset'):
            time.tzset()

        return True
    except Exception as e:
        print(f"时区设置失败: {e}")
        return False


def get_shanghai_timezone():
    """
    获取上海时区对象

    Returns:
        timezone: 上海时区对象 (UTC+8)
    """
    return timezone(timedelta(hours=8))


def verify_timezone():
    """
    验证时区设置是否正确
    
    Returns:
        dict: 包含验证结果的字典
    """
    try:
        current_time = datetime.now()
        tz_env = os.environ.get('TZ', 'Not Set')
        
        return {
            'success': True,
            'timezone_env': tz_env,
            'current_time': current_time.strftime('%Y-%m-%d %H:%M:%S'),
            'timezone_name': current_time.astimezone().tzname() if hasattr(current_time, 'astimezone') else 'Unknown'
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }


class ShanghaiTimeFormatter(logging.Formatter):
    """
    上海时区日志格式化器
    
    继承自logging.Formatter，确保日志时间显示为上海时间。
    使用datetime.now()获取本地时间，避免UTC时间问题。
    """
    
    def formatTime(self, record, datefmt=None):
        """
        格式化时间为上海时区时间

        Args:
            record: 日志记录对象
            datefmt: 日期格式字符串（可选）

        Returns:
            str: 格式化后的时间字符串
        """
        try:
            # 获取UTC时间并转换为上海时区
            utc_dt = datetime.now(timezone.utc)
            shanghai_tz = get_shanghai_timezone()
            shanghai_dt = utc_dt.astimezone(shanghai_tz)

            if datefmt:
                return shanghai_dt.strftime(datefmt)
            else:
                # 默认格式：YYYY-MM-DD HH:MM:SS
                return shanghai_dt.strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            # 如果出现异常，使用备用方法
            try:
                # 备用方法：直接计算UTC+8时间
                utc_dt = datetime.now(timezone.utc)
                shanghai_dt = utc_dt + timedelta(hours=8)
                return shanghai_dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                # 最后的备用方法
                return datetime.now().strftime('%Y-%m-%d %H:%M:%S')


def get_current_shanghai_time():
    """
    获取当前上海时间

    Returns:
        str: 格式化的上海时间字符串 (YYYY-MM-DD HH:MM:SS)
    """
    try:
        # 获取UTC时间并转换为上海时区
        utc_dt = datetime.now(timezone.utc)
        shanghai_tz = get_shanghai_timezone()
        shanghai_dt = utc_dt.astimezone(shanghai_tz)
        return shanghai_dt.strftime('%Y-%m-%d %H:%M:%S')
    except Exception:
        # 备用方法：直接计算UTC+8时间
        utc_dt = datetime.now(timezone.utc)
        shanghai_dt = utc_dt + timedelta(hours=8)
        return shanghai_dt.strftime('%Y-%m-%d %H:%M:%S')


def setup_logging_with_shanghai_time(logger_name=None, level=logging.INFO):
    """
    设置使用上海时间的日志配置
    
    Args:
        logger_name: 日志器名称，默认为None（根日志器）
        level: 日志级别，默认为INFO
        
    Returns:
        logging.Logger: 配置好的日志器对象
    """
    # 获取日志器
    logger = logging.getLogger(logger_name)
    
    # 如果已经有处理器，先清除
    if logger.handlers:
        logger.handlers.clear()
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    
    # 使用上海时区格式化器
    formatter = ShanghaiTimeFormatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    
    # 添加处理器到日志器
    logger.addHandler(console_handler)
    logger.setLevel(level)
    
    return logger


# 模块初始化时自动设置时区
if __name__ != '__main__':
    # 只有在被导入时才自动设置时区，避免在直接运行时重复设置
    setup_timezone()


# 测试代码（仅在直接运行此文件时执行）
if __name__ == '__main__':
    print("=== 时区配置模块测试 ===")
    
    # 测试时区设置
    print("1. 测试时区设置...")
    result = setup_timezone()
    print(f"   时区设置结果: {'成功' if result else '失败'}")
    
    # 测试时区验证
    print("2. 测试时区验证...")
    verify_result = verify_timezone()
    if verify_result['success']:
        print(f"   环境变量TZ: {verify_result['timezone_env']}")
        print(f"   当前时间: {verify_result['current_time']}")
        print(f"   时区名称: {verify_result['timezone_name']}")
    else:
        print(f"   验证失败: {verify_result['error']}")
    
    # 测试日志格式化器
    print("3. 测试日志格式化器...")
    test_logger = setup_logging_with_shanghai_time('test_logger')
    test_logger.info("这是一条测试日志，时间应该显示为上海时间")
    
    # 测试时间获取函数
    print("4. 测试时间获取函数...")
    current_time = get_current_shanghai_time()
    print(f"   当前上海时间: {current_time}")
    
    print("=== 测试完成 ===")
