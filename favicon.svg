<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1890ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#722ed1;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="16" cy="16" r="15" fill="url(#grad1)" stroke="#fff" stroke-width="1"/>
  
  <!-- 联通标志简化版 - 两个相连的圆环 -->
  <g fill="none" stroke="#fff" stroke-width="2.5" stroke-linecap="round">
    <!-- 左圆环 -->
    <circle cx="11" cy="13" r="4" opacity="0.9"/>
    <!-- 右圆环 -->
    <circle cx="21" cy="19" r="4" opacity="0.9"/>
    <!-- 连接线 -->
    <path d="M14.5 15.5 L17.5 16.5" stroke-width="2"/>
  </g>
  
  <!-- 中心点 -->
  <circle cx="16" cy="16" r="1.5" fill="#fff" opacity="0.8"/>
</svg>
