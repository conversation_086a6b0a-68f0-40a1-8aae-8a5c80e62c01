#!/bin/bash

# 联通查询系统停止脚本

echo "🛑 停止联通查询系统..."

# 检查Docker是否可用
if command -v docker &> /dev/null; then
    # 检查是否使用Docker Compose
    if [ -f "docker-compose.yml" ] && (command -v docker-compose &> /dev/null || docker compose version &> /dev/null); then
        echo "🐳 使用 Docker Compose 停止服务..."
        if command -v docker-compose &> /dev/null; then
            docker-compose down
        else
            docker compose down
        fi
    else
        # 使用普通Docker
        echo "🐳 停止 Docker 容器..."
        docker stop unicom-query 2>/dev/null || true
    fi
    
    echo "✅ Docker 服务已停止"
else
    # 停止Python进程
    echo "🐍 停止 Python 服务..."
    
    if pgrep -f "python.*server.py" > /dev/null; then
        pkill -f "python.*server.py"
        echo "✅ Python 服务已停止"
    else
        echo "ℹ️  没有发现运行中的服务"
    fi
fi

echo "🏁 停止完成"
