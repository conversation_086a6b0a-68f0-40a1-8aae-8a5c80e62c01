#!/bin/bash

# 联通查询系统一键部署脚本
# 支持Docker和直接部署两种方式

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

echo "========================================"
echo "联通查询系统 - 一键部署脚本"
echo "========================================"
echo

# 清理无用文件
cleanup_files() {
    log_step "清理无用文件..."

    # 删除.bat文件
    find . -name "*.bat" -type f -delete 2>/dev/null || true

    # 删除测试文件
    rm -f test_*.py 2>/dev/null || true
    rm -f *_test.py 2>/dev/null || true

    # 删除临时文件
    rm -f *.tmp 2>/dev/null || true
    rm -f *.log.* 2>/dev/null || true

    # 删除旧的数据文件（如果存在）
    rm -f lt_data.json 2>/dev/null || true

    # 注意：不删除数据库文件，保留用户数据
    # rm -f query_results.db 2>/dev/null || true

    # 删除旧的模板文件夹
    rm -rf templates/ 2>/dev/null || true

    # 删除Python缓存文件
    rm -rf __pycache__/ 2>/dev/null || true
    find . -name "*.pyc" -delete 2>/dev/null || true

    # 删除旧的启动脚本
    rm -f start.sh 2>/dev/null || true

    log_info "文件清理完成"
}

# 检查必要文件是否存在
echo "📁 检查项目文件..."
required_files=(
    "ltcx.py"
    "server.py"
    "index.html"
    "database.py"
    "favicon.svg"
    "timezone_config.py"
    "verify_deployment.sh"
    "Dockerfile"
    "docker-compose.yml"
    "requirements.txt"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -ne 0 ]; then
    echo "❌ 缺少必要文件："
    for file in "${missing_files[@]}"; do
        echo "   - $file"
    done
    exit 1
fi

echo "✅ 项目文件检查通过"
echo

# 清理文件
cleanup_files

# 检查Docker是否安装
if command -v docker &> /dev/null; then
    echo "✅ Docker 环境检查通过"
    echo

    # 检查是否安装了docker-compose
    if command -v docker-compose &> /dev/null || docker compose version &> /dev/null; then
        echo "🐳 使用 Docker Compose 部署..."

        # 停止现有服务
        echo "🔄 停止现有服务..."
        docker-compose down 2>/dev/null || docker compose down 2>/dev/null || true

        # 构建并启动服务
        echo "🚀 构建并启动服务..."
        if command -v docker-compose &> /dev/null; then
            docker-compose up -d --build
        else
            docker compose up -d --build
        fi

        if [ $? -eq 0 ]; then
            echo "✅ Docker Compose 部署成功！"
        else
            echo "❌ Docker Compose 部署失败，尝试使用普通 Docker..."
        fi
    else
        echo "🐳 使用普通 Docker 部署..."

        # 停止并删除现有容器
        echo "🔄 停止现有容器..."
        docker stop unicom-query 2>/dev/null || true
        docker rm unicom-query 2>/dev/null || true

        # 构建并启动容器
        echo "🚀 构建并启动容器..."
        if ! docker build -t unicom-query:latest .; then
            echo "❌ 容器构建失败，请检查错误信息"
            exit 1
        fi

        # 运行容器（添加首次部署环境变量和时区设置）
        if ! docker run -d \
            --name unicom-query \
            --restart unless-stopped \
            -p 2032:2032 \
            -e FIRST_DEPLOY=true \
            -e TZ=Asia/Shanghai \
            -v /etc/localtime:/etc/localtime:ro \
            -v /etc/timezone:/etc/timezone:ro \
            unicom-query:latest; then
            echo "❌ 容器启动失败，请检查错误信息"
            exit 1
        fi
    fi

    # 检查容器状态
    echo
    echo "📊 检查容器状态..."
    sleep 5

    # 检查容器是否在运行
    if docker ps | grep -q unicom-query; then
        echo "✅ 容器启动成功！"
        echo
        echo "🌐 访问地址："
        echo "   http://localhost:2032"
        echo
        echo "🔑 首次使用说明："
        echo "   1. 首次访问需要注册账号（严格限制只允许注册一次）"
        echo "   2. 注册成功后使用注册的账号登录"
        echo "   3. 登录有效期为365天，除非手动退出"
        echo "   4. 查询功能无需密码，直接点击查询即可"
        echo "   5. 注册限制：跨浏览器生效，系统级别防护"
        echo
        echo "✨ 系统特性："
        echo "   🎨 现代化UI设计，支持深色/浅色主题切换"
        echo "   🔐 严格注册限制：系统级别单次注册，跨浏览器生效"
        echo "   🛡️ 多重安全防护：前端+后端+数据库三层验证"
        echo "   📊 实时统计卡片，动态数据展示"
        echo "   📱 完全响应式设计，移动端卡片布局"
        echo "   🔄 后端独立定时：不依赖前端的自动查询系统"
        echo "   📈 实时进度显示，查询状态一目了然"
        echo "   🏷️ 完整手机号显示，便于管理"
        echo "   ⚡ 智能定时调度：默认每小时自动查询"
        echo "   🗄️ SQLite数据库存储，数据持久化保存"
        echo "   🚫 查询无需密码，简化操作流程"
        echo "   ⏰ 登录长期有效（365天），减少重复登录"
        echo "   🌐 网站图标：专业的联通主题SVG图标"
        echo
        echo "📋 常用命令："
        if command -v docker-compose &> /dev/null || docker compose version &> /dev/null; then
            echo "   查看日志: docker-compose logs -f 或 docker compose logs -f"
            echo "   停止服务: docker-compose down 或 docker compose down"
            echo "   重启服务: docker-compose restart 或 docker compose restart"
            echo "   重新部署: docker-compose up -d --build 或 docker compose up -d --build"
        else
            echo "   查看日志: docker logs unicom-query"
            echo "   停止服务: docker stop unicom-query"
            echo "   重启服务: docker restart unicom-query"
            echo "   重新部署: ./deploy.sh"
        fi
        echo
    else
        echo "❌ 容器启动失败，请检查日志："
        echo "   docker logs unicom-query"
        echo
        echo "🔍 常见问题排查："
        echo "   1. 检查端口2032是否被占用"
        echo "   2. 检查Docker资源是否充足"
        echo "   3. 检查依赖安装是否成功"
        exit 1
    fi
else
    echo "❌ Docker 未安装，尝试直接部署..."

    # 检查Python是否安装
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
        PIP_CMD="pip3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
        PIP_CMD="pip"
    else
        echo "❌ Python 未安装，请先安装 Python 或 Docker"
        exit 1
    fi

    echo "✅ Python 环境检查通过"
    echo

    # 安装依赖
    echo "📦 安装Python依赖..."
    $PIP_CMD install -r requirements.txt

    # 停止现有进程
    if pgrep -f "python.*server.py" > /dev/null; then
        echo "🔄 停止现有服务进程..."
        pkill -f "python.*server.py" || true
        sleep 2
    fi

    # 启动服务（设置首次部署环境变量）
    echo "🚀 启动联通查询服务..."
    FIRST_DEPLOY=true nohup $PYTHON_CMD server.py > server.log 2>&1 &

    # 等待服务启动
    sleep 3

    # 检查服务状态
    if pgrep -f "python.*server.py" > /dev/null; then
        echo "✅ 服务启动成功！"
        echo
        echo "🌐 访问地址："
        echo "   http://localhost:2032"
        echo
        echo "🔑 首次使用说明："
        echo "   1. 首次访问需要注册账号（严格限制只允许注册一次）"
        echo "   2. 注册成功后使用注册的账号登录"
        echo "   3. 登录有效期为365天，除非手动退出"
        echo "   4. 查询功能无需密码，直接点击查询即可"
        echo "   5. 注册限制：跨浏览器生效，系统级别防护"
        echo
        echo "📋 常用命令："
        echo "   查看日志: tail -f server.log"
        echo "   停止服务: pkill -f 'python.*server.py'"
        echo
    else
        echo "❌ 服务启动失败，请检查日志："
        echo "   cat server.log"
        exit 1
    fi
fi

echo "========================================"
echo "部署完成！"
echo "========================================"

# 运行部署验证
if [ -f "verify_deployment.sh" ]; then
    echo
    log_step "运行部署验证..."
    chmod +x verify_deployment.sh
    ./verify_deployment.sh
else
    echo
    log_info "🎉 部署完成！请手动验证系统功能"
fi
