version: '3.8'

services:
  unicom-query:
    build: .
    container_name: unicom-query
    restart: unless-stopped
    ports:
      - "2032:2032"
    environment:
      - TZ=Asia/Shanghai
      - FIRST_DEPLOY=true
      - PYTHONUNBUFFERED=1
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - /etc/localtime:/etc/localtime:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:2032/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - unicom-network

networks:
  unicom-network:
    driver: bridge

volumes:
  data:
  logs:
