# 使用Python官方镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV TZ=Asia/Shanghai

# 安装系统依赖并配置时区
RUN apt-get update && apt-get install -y \
    curl \
    tzdata \
    && ln -snf /usr/share/zoneinfo/$TZ /etc/localtime \
    && echo $TZ > /etc/timezone \
    && rm -rf /var/lib/apt/lists/*

# 验证时区设置
RUN echo "验证时区设置:" && \
    date && \
    echo "时区文件内容:" && \
    cat /etc/timezone

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY timezone_config.py .
COPY ltcx.py .
COPY server.py .
COPY index.html .
COPY database.py .
COPY favicon.svg .
COPY verify_deployment.sh .

# 创建数据和日志目录
RUN mkdir -p /app/data /app/logs

# 创建数据库目录并设置权限
RUN mkdir -p /app/db && chmod 755 /app/db

# 设置权限
RUN chmod +x server.py ltcx.py database.py timezone_config.py verify_deployment.sh

# 验证Python时区配置
RUN echo "验证Python时区配置:" && \
    python -c "import timezone_config; print('时区模块导入成功'); print('当前上海时间:', timezone_config.get_current_shanghai_time())"

# 暴露端口
EXPOSE 2032

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:2032/api/health || exit 1

# 启动命令
CMD ["python", "server.py"]
