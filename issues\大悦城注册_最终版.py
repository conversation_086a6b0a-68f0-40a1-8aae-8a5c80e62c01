import requests
import json
import time
import hashlib

def generate_nonce():
    """生成随机nonce"""
    import uuid
    return str(uuid.uuid4()).replace('-', '')

def generate_signature_v1(consumer, timestamp, nonce, tenant_id, open_id):
    """尝试方法1: 基本MD5签名"""
    sign_string = f"consumer={consumer}&timestamp={timestamp}&nonce={nonce}&tenantId={tenant_id}&openId={open_id}"
    return hashlib.md5(sign_string.encode()).hexdigest()

def generate_signature_v2(consumer, timestamp, nonce, tenant_id, open_id):
    """尝试方法2: 包含完整token参数的签名"""
    sign_string = f"consumer={consumer}&timestamp={timestamp}&nonce={nonce}&tenantId={tenant_id}&cid=&openId={open_id}&v=20220613"
    return hashlib.md5(sign_string.encode()).hexdigest()

def generate_signature_v3(consumer, timestamp, nonce, tenant_id, open_id):
    """尝试方法3: 参数字典序排序"""
    params = {
        'consumer': consumer,
        'timestamp': timestamp,
        'nonce': nonce,
        'tenantId': tenant_id,
        'openId': open_id
    }
    sorted_params = sorted(params.items())
    sign_string = '&'.join([f"{k}={v}" for k, v in sorted_params])
    return hashlib.md5(sign_string.encode()).hexdigest()

def generate_token_with_multiple_attempts(consumer="188880000002", tenant_id="100098706", open_id="oRuFi5Afjw0Nn69fObkQ0AWJxaZk"):
    """使用多种签名方法生成token并测试"""
    timestamp = str(int(time.time() * 1000))
    nonce = generate_nonce()
    
    # 尝试不同的签名方法
    signature_methods = [
        ("基本MD5", generate_signature_v1),
        ("完整参数MD5", generate_signature_v2),
        ("字典序MD5", generate_signature_v3),
    ]
    
    for method_name, sign_func in signature_methods:
        sign = sign_func(consumer, timestamp, nonce, tenant_id, open_id)
        token = f"consumer={consumer}&timestamp={timestamp}&nonce={nonce}&sign={sign}&tenantId={tenant_id}&cid=&openId={open_id}&v=20220613"
        
        print(f"\n尝试方法: {method_name}")
        print(f"签名: {sign}")
        
        # 测试这个token
        result = test_token(token)
        if result:
            response_code = result.get('code', 0)
            if response_code != 209:  # 不是签名错误
                print(f"✅ 方法 {method_name} 可能有效！")
                print(f"响应: {result}")
                return token, method_name
            else:
                print(f"❌ 方法 {method_name} 签名错误")
        else:
            print(f"❌ 方法 {method_name} 请求失败")
    
    print("\n所有方法都失败了，使用基本方法")
    sign = generate_signature_v1(consumer, timestamp, nonce, tenant_id, open_id)
    return f"consumer={consumer}&timestamp={timestamp}&nonce={nonce}&sign={sign}&tenantId={tenant_id}&cid=&openId={open_id}&v=20220613", "基本MD5"

def test_token(token):
    """测试token是否有效"""
    url = "https://appsmall.rtmap.com/wxapp-portal/memberApp/login/checkSms"
    
    payload = {
        "mobile": "17633509958",
        "zone": "86",
        "tenantId": "100098706",
        "vcode": "",
        "appId": "wxfb3ed1c5993a10ee",
        "openid": "oRuFi5Afjw0Nn69fObkQ0AWJxaZk",
        "tenantType": 1
    }
    
    headers = {
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) UnifiedPCWindowsWechat(0xf2540611) XWEB/16041",
        'Content-Type': "application/json",
        'xweb_xhr': "1",
        'token': token,
        'sec-fetch-site': "cross-site",
        'sec-fetch-mode': "cors",
        'sec-fetch-dest': "empty",
        'referer': "https://servicewechat.com/wxfb3ed1c5993a10ee/56/page-frame.html",
        'accept-language': "zh-CN,zh;q=0.9",
        'priority': "u=1, i"
    }
    
    try:
        response = requests.post(url, data=json.dumps(payload), headers=headers, timeout=10)
        return response.json() if response.text else None
    except Exception as e:
        print(f"请求异常: {e}")
        return None

def send_sms_verification():
    """发送短信验证码"""
    print("正在生成token...")
    token, method = generate_token_with_multiple_attempts()
    
    print(f"\n使用方法: {method}")
    print(f"Token: {token}")
    
    url = "https://appsmall.rtmap.com/wxapp-portal/memberApp/login/checkSms"
    
    payload = {
        "mobile": "17633509958",
        "zone": "86",
        "tenantId": "100098706",
        "vcode": "",
        "appId": "wxfb3ed1c5993a10ee",
        "openid": "oRuFi5Afjw0Nn69fObkQ0AWJxaZk",
        "tenantType": 1
    }
    
    headers = {
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) UnifiedPCWindowsWechat(0xf2540611) XWEB/16041",
        'Content-Type': "application/json",
        'xweb_xhr': "1",
        'token': token,
        'sec-fetch-site': "cross-site",
        'sec-fetch-mode': "cors",
        'sec-fetch-dest': "empty",
        'referer': "https://servicewechat.com/wxfb3ed1c5993a10ee/56/page-frame.html",
        'accept-language': "zh-CN,zh;q=0.9",
        'priority': "u=1, i"
    }
    
    try:
        response = requests.post(url, data=json.dumps(payload), headers=headers)
        print(f"\n发送验证码响应: {response.text}")
        return response.json() if response.text else None
    except Exception as e:
        print(f"发送验证码时出错: {e}")
        return None

def login_with_verification_code(vcode):
    """使用验证码登录"""
    print("正在生成登录token...")
    token, method = generate_token_with_multiple_attempts()
    
    url = "https://appsmall.rtmap.com/wxapp-portal/memberApp/login/checkSms"
    
    payload = {
        "mobile": "17633509958",
        "zone": "86",
        "tenantId": "100098706",
        "vcode": vcode,
        "appId": "wxfb3ed1c5993a10ee",
        "openid": "oRuFi5Afjw0Nn69fObkQ0AWJxaZk",
        "tenantType": 1
    }
    
    headers = {
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) UnifiedPCWindowsWechat(0xf2540611) XWEB/16041",
        'Content-Type': "application/json",
        'xweb_xhr': "1",
        'token': token,
        'sec-fetch-site': "cross-site",
        'sec-fetch-mode': "cors",
        'sec-fetch-dest': "empty",
        'referer': "https://servicewechat.com/wxfb3ed1c5993a10ee/56/page-frame.html",
        'accept-language': "zh-CN,zh;q=0.9",
        'priority': "u=1, i"
    }
    
    try:
        response = requests.post(url, data=json.dumps(payload), headers=headers)
        print(f"登录响应: {response.text}")
        return response.json() if response.text else None
    except Exception as e:
        print(f"登录时出错: {e}")
        return None

def main():
    """主函数"""
    print("=== 大悦城注册脚本 - 最终版 ===")
    print("自动尝试多种签名算法找到有效的方法\n")
    
    # 1. 发送验证码
    print("开始发送验证码...")
    sms_result = send_sms_verification()
    
    if not sms_result:
        print("发送验证码失败")
        return
    
    response_code = sms_result.get('code', 0)
    if response_code == 209:
        print("❌ 仍然是签名相关错误")
        return
    elif response_code == 200 or response_code == 0:
        print("✅ 验证码发送成功！")
    else:
        print(f"⚠️  收到响应码: {response_code}, 消息: {sms_result.get('message', '未知')}")
    
    # 2. 等待用户输入验证码
    vcode = input("\n请输入收到的验证码: ").strip()
    
    if not vcode:
        print("验证码不能为空")
        return
    
    # 3. 使用验证码登录
    print("正在登录...")
    login_result = login_with_verification_code(vcode)
    
    if login_result:
        if login_result.get('code') == 200 or login_result.get('code') == 0:
            print("✅ 登录成功!")
            print(f"完整响应数据: {json.dumps(login_result, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 登录失败: {login_result}")
    else:
        print("❌ 登录请求失败")

if __name__ == "__main__":
    main()
