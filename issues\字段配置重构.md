# 字段配置重构计划

## 背景
在 `间材采购订单涨价幅度.js` 中，`fields: ['zjfd', 'yin1']` 配置存在问题：
- 'zjfd' 是标准字段，有 WeFormSDK 绑定
- 'yin1' 看起来像自定义属性，仅在 DOM 操作中使用
- 需要重构以提高代码语义化和可读性

## 重构方案
采用配置对象结构化方案，清晰分离不同类型字段：
```javascript
const fieldConfig = {
    standardFields: ['zjfd'],    // 标准字段（有 SDK 绑定）
    domOnlyFields: ['yin1']      // 仅 DOM 操作字段
}
```

## 执行步骤
1. 创建 fieldConfig 配置对象
2. 重构 WeFormSDK 绑定逻辑
3. 重构 DOM 监听逻辑
4. 重构 formReady 处理
5. 添加注释说明