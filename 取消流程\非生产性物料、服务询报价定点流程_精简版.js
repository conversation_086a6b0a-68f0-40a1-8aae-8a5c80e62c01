// 非生产性物料、服务询报价定点流程 - 精简版
(function() {
    // 统一配置
    const CONFIG = {
        detailTables: ['xmxxmxb_845065503573', 'ft_1061832_fwxxmxb'],
        detailFields: ['je1', 'zjfd', 'zb1', 'zb2', 'zb3'],
        domFields: ['yin1'],
        domTables: ['table1', 'table2'],
        fieldCounts: { yarn: 30, supplier: 30, supplierDesc: 30 },
        
        // 动态生成字段数组
        getFields: (prefix, count) => Array.from({length: count}, (_, i) => `${prefix}${i + 1}`),
        get yarnFields() { return this.getFields('yarn', this.fieldCounts.yarn); },
        get supplierFields() { return this.getFields('supplier', this.fieldCounts.supplier); },
        get supplierDescFields() { return this.getFields('gysms', this.fieldCounts.supplierDesc); },
        get allFields() { return [...this.detailFields, ...this.domFields, ...this.yarnFields]; }
    };

    // 全局变量
    var flagHideArr = [], fieldValueArr = [];

    // 核心颜色设置方法 - 统一处理所有字段类型
    const setFieldColor = (field, value, fieldType = 'auto') => {
        if (!field) return;
        
        // 自动检测字段类型
        if (fieldType === 'auto') {
            const fieldId = field.getAttribute('id') || '';
            fieldType = fieldId === 'yin1' ? 'price' : 
                       fieldId.startsWith('yarn') ? 'yarn' : 'standard';
        }
        
        switch(fieldType) {
            case 'price':
                field.style.backgroundColor = parseFloat(value) > 0 ? "#E1FAEF" : "";
                break;
            case 'yarn':
                field.style.backgroundColor = value ? "#E1FAEF" : "";
                const yarnNum = field.getAttribute('id').replace('yarn', '');
                updateSupplierBackground(field, value.trim(), yarnNum);
                break;
            default:
                field.style.backgroundColor = value ? "#E1FAEF" : "";
        }
    };

    // 供应商背景更新
    const updateSupplierBackground = (field, value, supplierNum) => {
        const supplierElement = field.closest('tr')?.querySelector('#color_supplier' + supplierNum);
        if (supplierElement) {
            supplierElement.style.backgroundColor = value ? "#E1FAEF" : "";
        }
    };

    // 供应商标签更新 - 合并所有更新逻辑
    const updateSupplierLabel = (supplierNum, value) => {
        // 查找所有相关供应商元素
        const selectors = [
            ...CONFIG.domTables.map(id => `#${id} [id="supplier${supplierNum}"]`),
            `[id="supplier${supplierNum}"]`
        ];
        
        document.querySelectorAll(selectors.join(',')).forEach(element => {
            element.querySelectorAll('span').forEach(span => {
                if (value) {
                    span.textContent = value + ' ';
                    // 代理报价标记
                    const isProxy = value.includes('代理报价');
                    span.style.color = isProxy ? 'red' : '';
                    span.style.fontWeight = isProxy ? 'bold' : '';
                } else {
                    span.textContent = '供应商' + supplierNum;
                    span.style.color = span.style.fontWeight = '';
                }
            });
        });
    };

    // 初始化所有字段颜色 - 统一处理
    const initAllFieldColors = () => {
        [...CONFIG.domFields, ...CONFIG.yarnFields].forEach(fieldId => {
            document.querySelectorAll(`[id="${fieldId}"]`).forEach(field => {
                const input = field.querySelector("input, span") || field;
                const value = input.textContent || input.value || "";
                setFieldColor(field, value);
            });
        });
    };

    // WeFormSDK事件绑定 - 简化绑定逻辑
    const initWeFormSDKBindings = () => {
        if (!window.WeFormSDK || window._boundTables) return;
        
        window._boundTables = true;
        const sdk = window.WeFormSDK.getWeFormInstance();

        // 绑定明细表字段
        CONFIG.detailTables.forEach(tableName => {
            const tableId = sdk.convertFieldNameToId(tableName);
            CONFIG.detailFields.forEach(fieldName => {
                const fieldMark = sdk.convertFieldNameToId(fieldName, tableId);
                sdk.bindFieldChangeEvent(fieldMark, (data) => {
                    document.querySelectorAll('[fieldid="' + data.id.slice(5) + '"]')
                        .forEach(field => setFieldColor(field, data.value));
                });
            });
        });

        // 绑定供应商描述字段
        CONFIG.supplierDescFields.forEach((fieldName, index) => {
            const fieldId = sdk.convertFieldNameToId(fieldName, "main");
            sdk.bindFieldChangeEvent(fieldId, (data) => {
                fieldValueArr[index] = data.value || "";
                flagHideArr[index] = !!data.value;
                updateSupplierLabel(index + 1, data.value);
                initAllFieldColors();
            });
        });
    };

    // 事件委托 - 统一处理所有字段事件
    const setupEventDelegation = () => {
        const fieldSelector = CONFIG.allFields.map(f => `[id="${f}"]`).join(',');
        
        ['input', 'change'].forEach(eventType => {
            document.addEventListener(eventType, (e) => {
                const field = e.target.closest(fieldSelector);
                if (field) {
                    const value = e.target.value || e.target.textContent || "";
                    setFieldColor(field, value);
                }
            }, true);
        });
    };

    // MutationObserver - 简化DOM变化监听
    const setupMutationObserver = () => {
        const observer = new MutationObserver((mutations) => {
            let shouldUpdate = false;
            
            mutations.forEach(mutation => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === 1) {
                            // 检查是否包含目标字段或表格
                            const hasTargetElements = CONFIG.allFields.some(fieldId =>
                                node.querySelector?.(`[id="${fieldId}"]`) || node.id === fieldId
                            ) || CONFIG.domTables.some(tableId =>
                                node.querySelector?.(`#${tableId}`) || node.id === tableId
                            ) || node.querySelector?.('table, tr') || ['TABLE', 'TR'].includes(node.tagName);
                            
                            if (hasTargetElements) shouldUpdate = true;
                        }
                    });
                }
            });
            
            if (shouldUpdate) {
                setTimeout(() => {
                    initAllFieldColors();
                    updateAllSupplierLabels();
                }, 100);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'class']
        });
    };

    // 初始化供应商数据并更新所有标签
    const initSupplierDescriptions = () => {
        if (!window.WeFormSDK) return;
        
        const sdk = window.WeFormSDK.getWeFormInstance();
        fieldValueArr = [];
        flagHideArr = [];

        CONFIG.supplierDescFields.forEach((fieldName, index) => {
            const fieldId = sdk.convertFieldNameToId(fieldName, "main");
            const fieldValue = sdk.getFieldValue(fieldId);
            fieldValueArr[index] = fieldValue || "";
            flagHideArr[index] = !!fieldValue;
        });

        setTimeout(() => {
            updateAllSupplierLabels();
            initAllFieldColors();
        }, 1000);
    };

    // 更新所有供应商标签
    const updateAllSupplierLabels = () => {
        fieldValueArr.forEach((value, index) => {
            updateSupplierLabel(index + 1, value);
        });
    };

    // 表格操作监听 - 简化监听逻辑
    const setupTableListeners = () => {
        // 窗口大小变化
        window.addEventListener('resize', () => {
            setTimeout(() => {
                updateAllSupplierLabels();
                initAllFieldColors();
            }, 300);
        });

        // 表格相关点击事件
        document.addEventListener('click', (e) => {
            const isTableAction = ['.table-expand', '.table-zoom', '[class*="expand"]', 
                                 '[class*="zoom"]', '[onclick*="table"]', '[title*="放大"]', 
                                 '[title*="缩小"]'].some(selector => e.target.closest(selector));
            
            if (isTableAction) {
                setTimeout(() => {
                    updateAllSupplierLabels();
                    initAllFieldColors();
                }, 500);
            }
        });

        // 定期检查供应商标签状态
        setInterval(() => {
            let needsUpdate = false;
            fieldValueArr.forEach((expectedValue, index) => {
                if (expectedValue) {
                    const elements = document.querySelectorAll(`[id="supplier${index + 1}"] span`);
                    elements.forEach(span => {
                        if (!span.textContent.includes(expectedValue)) {
                            needsUpdate = true;
                        }
                    });
                }
            });
            
            if (needsUpdate) updateAllSupplierLabels();
        }, 2000);
    };

    // 保持原有工具方法兼容性
    window.hideDetail = function(weFormSdk, fieldName, className) {
        const detailMark = weFormSdk.convertFieldNameToId(fieldName);
        const detailRowStr = weFormSdk.getDetailRowCount(detailMark);

        if (detailRowStr == 0) {
            document.querySelectorAll('.' + className).forEach(item => {
                item.style.display = 'none';
            });
        }
    };

    // 主初始化函数
    const init = () => {
        initWeFormSDKBindings();
        initAllFieldColors();
        setupEventDelegation();
        setupMutationObserver();
        setupTableListeners();
        
        if (window.WeFormSDK) {
            initSupplierDescriptions();
        }
    };

    // 页面就绪事件处理
    if (typeof pageSdk !== 'undefined') {
        pageSdk.on('formReady', (args) => {
            setTimeout(() => {
                initSupplierDescriptions();
                initAllFieldColors();
                console.warn('changeColor completed for all tables');
            }, 3000);
        });
    }

    // 启动初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // 兼容性检查
    if (window.WeFormSDK) {
        console.warn("window.WeFormSDK loaded");
    }

})();