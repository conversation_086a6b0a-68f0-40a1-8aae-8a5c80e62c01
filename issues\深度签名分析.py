import hashlib
import hmac
import base64
import urllib.parse

def deep_signature_analysis():
    """深度分析签名算法"""
    
    # curl中的实际参数
    consumer = "188880000002"
    timestamp = "1752637422784"
    nonce = "9c7b473eef2946b4839f4e5361250699"
    tenant_id = "100098706"
    open_id = "oRuFi5Afjw0Nn69fObkQ0AWJxaZk"
    expected_sign = "f1dce0b8cdbd52c6cb828ee96e4088c3"
    
    # 从curl中提取的其他可能相关参数
    app_id = "wxfb3ed1c5993a10ee"
    mobile = "17633509958"
    zone = "86"
    vcode = "5516"
    tenant_type = "1"
    v = "20220613"
    cid = ""
    
    print(f"目标签名: {expected_sign}")
    print("=" * 60)
    
    # 测试1: 包含POST数据的签名
    def test_with_post_data():
        print("\n=== 测试包含POST数据的签名 ===")
        post_data = f'{{"mobile":"{mobile}","zone":"{zone}","tenantId":"{tenant_id}","vcode":"{vcode}","appId":"{app_id}","openid":"{open_id}","tenantType":{tenant_type}}}'
        
        # 方法1: 参数 + POST数据
        sign_string = f"consumer={consumer}&timestamp={timestamp}&nonce={nonce}&tenantId={tenant_id}&openId={open_id}{post_data}"
        sign = hashlib.md5(sign_string.encode()).hexdigest()
        print(f"参数+POST: {sign_string}")
        print(f"结果: {sign}")
        if sign == expected_sign:
            return True
            
        # 方法2: POST数据 + 参数
        sign_string = f"{post_data}consumer={consumer}&timestamp={timestamp}&nonce={nonce}&tenantId={tenant_id}&openId={open_id}"
        sign = hashlib.md5(sign_string.encode()).hexdigest()
        print(f"POST+参数: {sign_string}")
        print(f"结果: {sign}")
        if sign == expected_sign:
            return True
        
        return False
    
    # 测试2: URL编码测试
    def test_url_encoding():
        print("\n=== 测试URL编码 ===")
        # URL编码openId
        encoded_open_id = urllib.parse.quote(open_id)
        sign_string = f"consumer={consumer}&timestamp={timestamp}&nonce={nonce}&tenantId={tenant_id}&openId={encoded_open_id}"
        sign = hashlib.md5(sign_string.encode()).hexdigest()
        print(f"URL编码: {sign_string}")
        print(f"结果: {sign}")
        if sign == expected_sign:
            return True
        return False
    
    # 测试3: 包含HTTP方法和URL
    def test_with_http_info():
        print("\n=== 测试包含HTTP信息 ===")
        method = "POST"
        url = "https://appsmall.rtmap.com/wxapp-portal/memberApp/login/checkSms"
        
        # 方法1: 方法 + URL + 参数
        sign_string = f"{method}{url}consumer={consumer}&timestamp={timestamp}&nonce={nonce}&tenantId={tenant_id}&openId={open_id}"
        sign = hashlib.md5(sign_string.encode()).hexdigest()
        print(f"方法+URL+参数: {sign_string}")
        print(f"结果: {sign}")
        if sign == expected_sign:
            return True
            
        # 方法2: 只包含URL路径
        path = "/wxapp-portal/memberApp/login/checkSms"
        sign_string = f"{method}{path}consumer={consumer}&timestamp={timestamp}&nonce={nonce}&tenantId={tenant_id}&openId={open_id}"
        sign = hashlib.md5(sign_string.encode()).hexdigest()
        print(f"方法+路径+参数: {sign_string}")
        print(f"结果: {sign}")
        if sign == expected_sign:
            return True
        
        return False
    
    # 测试4: 不同的哈希算法
    def test_different_hash():
        print("\n=== 测试不同哈希算法 ===")
        sign_string = f"consumer={consumer}&timestamp={timestamp}&nonce={nonce}&tenantId={tenant_id}&openId={open_id}"
        
        # SHA1
        sign = hashlib.sha1(sign_string.encode()).hexdigest()
        print(f"SHA1: {sign}")
        if sign == expected_sign:
            return True
            
        # SHA256
        sign = hashlib.sha256(sign_string.encode()).hexdigest()
        print(f"SHA256: {sign}")
        if sign == expected_sign:
            return True
            
        # SHA1前32位
        sign = hashlib.sha1(sign_string.encode()).hexdigest()[:32]
        print(f"SHA1前32位: {sign}")
        if sign == expected_sign:
            return True
        
        return False
    
    # 测试5: Base64编码测试
    def test_base64():
        print("\n=== 测试Base64编码 ===")
        sign_string = f"consumer={consumer}&timestamp={timestamp}&nonce={nonce}&tenantId={tenant_id}&openId={open_id}"
        
        # Base64编码后MD5
        encoded = base64.b64encode(sign_string.encode()).decode()
        sign = hashlib.md5(encoded.encode()).hexdigest()
        print(f"Base64编码: {encoded}")
        print(f"结果: {sign}")
        if sign == expected_sign:
            return True
        
        return False
    
    # 测试6: 可能的固定secret key
    def test_fixed_secrets():
        print("\n=== 测试可能的固定secret ===")
        possible_secrets = [
            "rtmap2022",
            "rtmap2021", 
            "rtmap2020",
            "appsmall",
            "wxapp",
            "portal",
            "memberApp",
            "login",
            "checkSms",
            "dayuecheng",
            "大悦城",
            "188880000002secret",
            "100098706secret",
            "rtmapkey",
            "rtmapsecret",
            "key2022",
            "secret2022",
        ]
        
        base_string = f"consumer={consumer}&timestamp={timestamp}&nonce={nonce}&tenantId={tenant_id}&openId={open_id}"
        
        for secret in possible_secrets:
            # 前缀
            test_string = secret + base_string
            sign = hashlib.md5(test_string.encode()).hexdigest()
            print(f"前缀({secret}): {sign}")
            if sign == expected_sign:
                print(f"✅ 找到正确secret前缀: {secret}")
                return True
                
            # 后缀
            test_string = base_string + secret
            sign = hashlib.md5(test_string.encode()).hexdigest()
            print(f"后缀({secret}): {sign}")
            if sign == expected_sign:
                print(f"✅ 找到正确secret后缀: {secret}")
                return True
        
        return False
    
    # 测试7: 时间戳变体
    def test_timestamp_variants():
        print("\n=== 测试时间戳变体 ===")
        # 秒级时间戳
        timestamp_s = str(int(int(timestamp) / 1000))
        sign_string = f"consumer={consumer}&timestamp={timestamp_s}&nonce={nonce}&tenantId={tenant_id}&openId={open_id}"
        sign = hashlib.md5(sign_string.encode()).hexdigest()
        print(f"秒级时间戳: {sign_string}")
        print(f"结果: {sign}")
        if sign == expected_sign:
            return True
        
        return False
    
    # 执行所有测试
    tests = [
        ("包含POST数据", test_with_post_data),
        ("URL编码测试", test_url_encoding),
        ("HTTP信息测试", test_with_http_info),
        ("不同哈希算法", test_different_hash),
        ("Base64编码", test_base64),
        ("固定secret测试", test_fixed_secrets),
        ("时间戳变体", test_timestamp_variants),
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ 找到正确算法: {test_name}")
                return test_name
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n❌ 深度分析也未能找到正确的签名算法")
    return None

if __name__ == "__main__":
    print("=== 大悦城签名算法深度分析 ===")
    result = deep_signature_analysis()
    if result:
        print(f"\n🎉 成功找到签名算法: {result}")
    else:
        print("\n😞 需要更多信息或者算法更复杂")
