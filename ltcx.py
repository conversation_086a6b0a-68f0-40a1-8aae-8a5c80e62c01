#!/usr/bin/env python3
# 联通查询兑换脚本版 - 自动化查询和兑换红包
# 使用内置手机号或远程获取

# 兼容性导入 - 支持不同环境下的pycryptodome
try:
    from Crypto.Cipher import AES
    from Crypto.Util.Padding import pad
except ImportError:
    try:
        from Cryptodome.Cipher import AES
        from Cryptodome.Util.Padding import pad
    except ImportError:
        raise ImportError("无法导入加密库，请安装 pycryptodome: pip install pycryptodome")
import binascii
import base64
import json
import requests
import re
import os
import sys
import argparse
import time
import uuid
from datetime import datetime, timedelta
import logging

# 导入统一时区配置模块
from timezone_config import setup_timezone, ShanghaiTimeFormatter, get_current_shanghai_time

# 设置时区
setup_timezone()

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('ltcx.log')
    ]
)
logger = logging.getLogger(__name__)

# 为所有处理器设置上海时区格式化器
formatter = ShanghaiTimeFormatter('%(asctime)s - %(levelname)s - %(message)s')
for handler in logger.handlers:
    handler.setFormatter(formatter)

# 配置变量
CONFIG = {
    'AES_KEY': os.getenv('AES_KEY', 'woreadst^&*12345'),
    'AES_IV': os.getenv('AES_IV', '16-Bytes--String'),
    'ACCESS_TOKEN': os.getenv('ACCESS_TOKEN', 'ODZERTZCMjA1NTg1MTFFNDNFMThDRDYw'),
    'EXCHANGE_THRESHOLDS': json.loads(os.getenv('EXCHANGE_THRESHOLDS', '[5.0]')),
    'AUTO_EXCHANGE': os.getenv('AUTO_EXCHANGE', 'true').lower() == 'true',
    'REMOTE_URL': os.getenv('REMOTE_URL', 'https://alist.201626.xyz/d/%E8%8B%A5%E5%AF%92/1/ck.txt?sign=bRLggerXCz6_Ld64ADyaqkHgwEgzimYQn7J8MimZhCo=:0'),  # 远程URL配置
    'DEFAULT_PHONES': [  # 默认手机号列表
        # '17633509958',
        # '17771074627',
        # '15626421515',
        # '18739027625',
        # '13385410525',
        # '18845105658',
        # '17341238147',
        # '16685105259'
    ]
}

# 兼容旧配置，将被弃用
if 'EXCHANGE_THRESHOLDS' not in CONFIG:
    CONFIG['EXCHANGE_THRESHOLDS'] = [
        float(os.getenv('EXCHANGE_THRESHOLD_5', '5.0')),
        float(os.getenv('EXCHANGE_THRESHOLD_2', '2.0'))
    ]

# 加密函数
def encrypt(plain_text, key, iv):
    key = key.encode('utf-8')
    iv = iv.encode('utf-8')
    cipher = AES.new(key, AES.MODE_CBC, iv)
    padded_text = pad(plain_text.encode('utf-8'), AES.block_size, style='pkcs7')
    encrypted_text = cipher.encrypt(padded_text)
    encrypted = binascii.hexlify(encrypted_text).decode('utf-8')
    encoded_bytes = base64.b64encode(encrypted.encode('utf-8'))
    encoded_str = encoded_bytes.decode('utf-8')
    return encoded_str

# 从远程URL获取手机号列表
def fetch_phone_numbers(url):
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        content = response.text.strip()
        
        # 尝试解析为JSON格式
        try:
            data = json.loads(content)
            if isinstance(data, list):
                # 如果是列表，直接返回
                return data
            elif isinstance(data, dict) and 'phones' in data:
                # 如果是字典且包含phones键
                return data['phones']
        except json.JSONDecodeError:
            # 不是JSON格式，按行分割
            return content.split('\n')
    except Exception as e:
        logger.error(f"获取远程手机号失败: {e}")
        return None

# 获取手机号列表（按优先级：环境变量 > 远程URL > 默认列表）
def get_phone_numbers():
    # 1. 尝试从环境变量获取
    env_phones = os.getenv('zgltck1', '')
    if env_phones:
        phones = [p.strip() for p in env_phones.split('\n') if p.strip()]
        if phones:
            logger.info(f"从环境变量获取到 {len(phones)} 个手机号")
            return phones
    
    # 2. 尝试从远程URL获取
    if CONFIG['REMOTE_URL']:
        remote_phones = fetch_phone_numbers(CONFIG['REMOTE_URL'])
        if remote_phones:
            # 过滤空值并去重
            phones = list(set([p.strip() for p in remote_phones if p.strip()]))
            if phones:
                logger.info(f"从远程URL获取到 {len(phones)} 个手机号")
                return phones
    
    # 3. 使用默认手机号列表
    logger.info("使用默认手机号列表")
    return CONFIG['DEFAULT_PHONES']

# 保存数据到文件
def save_data(data, filename):
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"保存数据失败: {e}")
        return False

# 从文件读取数据
def load_data(filename):
    try:
        if not os.path.exists(filename):
            return {}
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"读取数据失败: {e}")
        return {}

class LT:
    def __init__(self):
        self.userindex = None
        self.verifycode = None
        self.usid = None
        self.userid = None
        self.token = None
        self.phone = None
        
        # 存储所有手机号的结果
        self.results = {}
        
        # 存储路径
        self.data_file = 'lt_data.json'
        
        # 加载已保存的数据
        self.data = load_data(self.data_file)

    # API请求函数（添加超时和重试机制）
    def task(self, method, url, headers, data, timeout=10, max_retries=2):
        for attempt in range(max_retries + 1):
            try:
                if method.lower() == 'post':
                    response = requests.post(url, headers=headers, json=data, timeout=timeout)
                else:
                    response = requests.get(url, headers=headers, timeout=timeout)
                response.raise_for_status()
                return response.json()
            except requests.Timeout:
                if attempt < max_retries:
                    logger.warning(f"请求超时，正在重试 ({attempt + 1}/{max_retries}): {url}")
                    continue
                else:
                    logger.error(f"请求超时，已达最大重试次数: {url}")
                    return {"code": "timeout", "message": "请求超时"}
            except requests.RequestException as e:
                if attempt < max_retries:
                    logger.warning(f"请求失败，正在重试 ({attempt + 1}/{max_retries}): {url} - {e}")
                    continue
                else:
                    logger.error(f"API请求失败: {url} - {e}")
                    return {"code": "error", "message": str(e)}

    # 登录函数（添加错误处理）
    def login(self, phone):
        logger.info(f"尝试登录账号: {phone[:3]}***{phone[7:]}")
        self.phone = phone

        try:
            now = datetime.now()
            formatted_datetime = now.strftime("%Y%m%d%H%M%S")
            key = CONFIG['AES_KEY']
            iv = CONFIG['AES_IV']

            PH = encrypt(phone, key, iv)
            str_to_encrypt = f'{{"phone":"{PH}","timestamp":"{formatted_datetime}"}}'
            headers = {'accesstoken': CONFIG['ACCESS_TOKEN']}
            encrypted_data = encrypt(str_to_encrypt, key, iv)
            body = {'sign': encrypted_data}

            response = self.task('post', 'https://10010.woread.com.cn/ng_woread_service/rest/account/login', headers, body, timeout=15)

            # 检查响应是否有效
            if not response or not isinstance(response, dict):
                logger.error(f"账号 {phone[:3]}***{phone[7:]} 登录失败: 无效响应")
                return False

            if response.get('code') == '0000' and 'data' in response:
                data = response['data']
                if all(key in data for key in ['userindex', 'verifycode', 'userid', 'token']):
                    self.userindex = data['userindex']
                    self.verifycode = data['verifycode']
                    self.userid = data['userid']
                    self.token = data['token']
                    logger.info(f"账号 {phone[:3]}***{phone[7:]} 登录成功")
                    return True
                else:
                    logger.error(f"账号 {phone[:3]}***{phone[7:]} 登录失败: 响应数据不完整")
                    return False
            else:
                error_msg = response.get('message', '未知错误')
                if response.get('code') == 'timeout':
                    logger.error(f"账号 {phone[:3]}***{phone[7:]} 登录超时，跳过此账号")
                else:
                    logger.error(f"账号 {phone[:3]}***{phone[7:]} 登录失败: {error_msg}")
                return False

        except Exception as e:
            logger.error(f"账号 {phone[:3]}***{phone[7:]} 登录异常: {e}")
            return False

    # 查询账户信息
    def userinfo(self, phone):
        now = datetime.now()
        formatted_date = now.strftime("%Y%m%d%H%M%S")
        data = {
            "timestamp": formatted_date, 
            "token": self.token, 
            "userId": self.userid, 
            "userIndex": self.userindex,
            "userAccount": phone, 
            "verifyCode": self.verifycode
        }
        
        key = CONFIG['AES_KEY']
        iv = CONFIG['AES_IV']
        encrypted_data = encrypt(json.dumps(data), key, iv)
        headers = {"accesstoken": CONFIG['ACCESS_TOKEN']}
        body = {"sign": encrypted_data}

        response = self.task(
            "post",
            "https://10010.woread.com.cn/ng_woread_service/rest/phone/vouchers/queryTicketAccount",
            headers, body
        )
        
        if response['code'] == "0000":
            total = response['data']['totalNum'] / 100
            available = response['data']['usableNum'] / 100
            logger.info(f"账号 {phone[:3]}***{phone[7:]} 当前余额: {available}元, 历史累计: {total}元")
            return total, available
        else:
            logger.error(f"查询账户信息失败: {response.get('message', '未知错误')}")
            return 0, 0

    # 查询话费券
    def hfq(self, phone):
        now = datetime.now()
        formatted_date = now.strftime("%Y%m%d%H%M%S")
        data = {
            "timestamp": formatted_date, 
            "token": self.token, 
            "userId": self.userid, 
            "userIndex": self.userindex,
            "userAccount": phone, 
            "verifyCode": self.verifycode
        }
        
        key = CONFIG['AES_KEY']
        iv = CONFIG['AES_IV']
        encrypted_data = encrypt(json.dumps(data), key, iv)
        headers = {"accesstoken": CONFIG['ACCESS_TOKEN']}
        body = {"sign": encrypted_data}

        response = self.task(
            "post",
            "https://10010.woread.com.cn/ng_woread_service/rest/phone/vouchers/queryPhoneTicketList",
            headers, body
        )
        
        if response['code'] == "0000":
            # 查询话费券列表成功，拼接待使用话费券信息
            arr = response.get('list', [])
            result = ''
            total_amount = 0
            vouchers = []
            
            # 收集并处理话费券信息
            for ticket_info in arr:
                status = ticket_info.get('ticketState')
                if status == "A":  # 可用状态
                    amount = ticket_info.get('ticketAmt')
                    expire_date = ticket_info.get('expireDate')
                    if expire_date:
                        try:
                            expire_date_obj = datetime.strptime(expire_date, '%Y%m%d')
                            days_left = (expire_date_obj - now).days
                            if days_left < 5:
                                expire_date = f"{expire_date}❗❗❗"
                        except ValueError:
                            pass
                    total_amount += int(float(amount))
                    vouchers.append({
                        'amount': amount,
                        'expire_date': expire_date
                    })
            
            # 按过期时间排序
            vouchers.sort(key=lambda x: x['expire_date'].replace('❗', '') if isinstance(x['expire_date'], str) else x['expire_date'])
            
            # 生成排序后的结果字符串
            for voucher in vouchers:
                result += f"{int(float(voucher['amount']))}/{voucher['expire_date']}\n"
            
            if total_amount > 0:
                result += f"总未使用金额: {total_amount}元"
            
            return result
        else:
            logger.error(f"查询话费券失败: {response.get('message', '未知错误')}")
            return ''

    # 查询任务状态
    def rwzt(self, phone):
        now = datetime.now()
        formatted_date = now.strftime("%Y%m%d%H%M%S")
        data = {
            "activeIndex": "22", 
            "timestamp": formatted_date, 
            "token": self.token, 
            "userId": self.userid, 
            "userIndex": self.userindex,
            "userAccount": phone, 
            "verifyCode": self.verifycode
        }
        
        key = CONFIG['AES_KEY']
        iv = CONFIG['AES_IV']
        encrypted_data = encrypt(json.dumps(data), key, iv)
        headers = {"accesstoken": CONFIG['ACCESS_TOKEN']}
        body = {"sign": encrypted_data}

        response = self.task(
            "post",
            "https://10010.woread.com.cn/ng_woread_service/rest/activity423/queryCurTaskStatus",
            headers, body
        )
        
        if response['code'] == "0000":
            arr = response['data']
            tasks = []
            
            for item in arr:
                task_name = item['taskDetail']['materialGroup']['bindActiveName']
                
                # 确保current和total都是整数类型
                try:
                    current = int(item['taskDetail']['currentValue'])
                except (ValueError, TypeError):
                    current = 0
                    
                try:
                    total = int(item['taskDetail']['taskValue'])
                except (ValueError, TypeError):
                    total = 0
                    
                tasks.append({
                    'name': task_name,
                    'current': current,
                    'total': total,
                    'completed': current >= total
                })
                logger.debug(f"任务: {task_name}, 进度: {current}/{total}")
            
            return tasks
        else:
            message = response.get('message', '未知错误')
            if '未领取任务' in message:
                logger.info(f"账号未领取任务: {message}")
            else:
                logger.error(f"查询任务状态失败: {message}")
            return []

    # 兑换5元红包
    def dh5r(self, phone):
        now = datetime.now()
        formatted_date = now.strftime("%Y%m%d%H%M%S")
        data = {
            "ticketValue": "500",
            "activeid": "61yd210901", 
            "timestamp": formatted_date, 
            "token": self.token, 
            "userId": self.userid, 
            "userIndex": self.userindex,
            "userAccount": phone, 
            "verifyCode": self.verifycode
        }
        
        key = CONFIG['AES_KEY']
        iv = CONFIG['AES_IV']
        encrypted_data = encrypt(json.dumps(data), key, iv)
        headers = {"accesstoken": CONFIG['ACCESS_TOKEN']}
        body = {"sign": encrypted_data}

        response = self.task(
            "post",
            "https://10010.woread.com.cn/ng_woread_service/rest/phone/vouchers/exchange",
            headers, body
        )
        
        result = response['message']
        logger.info(f"账号 {phone[:3]}***{phone[7:]} 兑换5元红包结果: {result}")
        return result

    # 兑换2元红包
    def dh2r(self, phone):
        now = datetime.now()
        formatted_date = now.strftime("%Y%m%d%H%M%S")
        data = {
            "ticketValue": "200",
            "activeid": "61yd210901", 
            "timestamp": formatted_date, 
            "token": self.token, 
            "userId": self.userid, 
            "userIndex": self.userindex,
            "userAccount": phone, 
            "verifyCode": self.verifycode
        }
        
        key = CONFIG['AES_KEY']
        iv = CONFIG['AES_IV']
        encrypted_data = encrypt(json.dumps(data), key, iv)
        headers = {"accesstoken": CONFIG['ACCESS_TOKEN']}
        body = {"sign": encrypted_data}

        response = self.task(
            "post",
            "https://10010.woread.com.cn/ng_woread_service/rest/phone/vouchers/exchange",
            headers, body
        )
        
        result = response['message']
        logger.info(f"账号 {phone[:3]}***{phone[7:]} 兑换2元红包结果: {result}")
        return result

    # 查询所有手机号信息（添加超时和错误处理）
    def query_all(self, phones):
        results = {}
        total_phones = len(phones)

        for index, phone in enumerate(phones, 1):
            logger.info(f"正在查询第 {index}/{total_phones} 个账号: {phone[:3]}***{phone[7:]}")

            if not re.compile(r'^1[3-9]\d{9}$').match(phone):
                logger.warning(f"手机号格式错误: {phone}")
                continue

            try:
                # 单个账号查询，添加异常处理
                if self.login(phone):
                    total, available = self.userinfo(phone)
                    vouchers = self.hfq(phone)
                    tasks = self.rwzt(phone)

                    # 存储结果
                    results[phone] = {
                        'total': total,
                        'available': available,
                        'vouchers': vouchers,
                        'tasks': tasks,
                        'last_query': get_current_shanghai_time()
                    }

                    # 保存到实例数据
                    self.results[phone] = results[phone]

                    # 检查是否需要自动兑换
                    if CONFIG['AUTO_EXCHANGE']:
                        self.auto_exchange(phone, available)

                    logger.info(f"账号 {phone[:3]}***{phone[7:]} 查询完成")
                else:
                    logger.error(f"账号 {phone[:3]}***{phone[7:]} 登录失败")
                    # 即使登录失败也记录结果，避免数据丢失
                    results[phone] = {
                        'total': 0,
                        'available': 0,
                        'vouchers': '登录失败',
                        'tasks': [],
                        'last_query': get_current_shanghai_time()
                    }
                    self.results[phone] = results[phone]

            except Exception as e:
                logger.error(f"账号 {phone[:3]}***{phone[7:]} 查询异常: {e}")
                # 记录异常结果
                results[phone] = {
                    'total': 0,
                    'available': 0,
                    'vouchers': f'查询异常: {str(e)}',
                    'tasks': [],
                    'last_query': get_current_shanghai_time()
                }
                self.results[phone] = results[phone]

            # 避免请求过快，给服务器一些缓冲时间
            time.sleep(2)

        logger.info(f"所有账号查询完成，成功 {len([r for r in results.values() if r.get('total', 0) > 0])} 个，失败 {len([r for r in results.values() if r.get('total', 0) == 0])} 个")

        # 只更新内存数据，不保存到文件
        self.data.update(results)

        return results

    # 查询所有手机号信息（带进度更新）
    def query_all_with_progress(self, phones, query_id):
        results = {}
        total_phones = len(phones)

        for index, phone in enumerate(phones, 1):
            logger.info(f"正在查询第 {index}/{total_phones} 个账号: {phone[:3]}***{phone[7:]}")

            # 更新进度状态（查询开始前）
            progress = int(((index - 1) / total_phones) * 85) + 10  # 10-95%的进度范围
            with query_lock:
                if query_id in query_status:
                    query_status[query_id].update({
                        'progress': progress,
                        'message': f'正在查询第 {index}/{total_phones} 个账号...',
                        'processed_accounts': index - 1,
                        'total_accounts': total_phones
                    })

            if not re.compile(r'^1[3-9]\d{9}$').match(phone):
                logger.warning(f"手机号格式错误: {phone}")
                continue

            try:
                # 单个账号查询，添加异常处理
                if self.login(phone):
                    total, available = self.userinfo(phone)
                    vouchers = self.hfq(phone)
                    tasks = self.rwzt(phone)

                    # 存储结果
                    results[phone] = {
                        'total': total,
                        'available': available,
                        'vouchers': vouchers,
                        'tasks': tasks,
                        'last_query': get_current_shanghai_time()
                    }

                    # 保存到实例数据
                    self.results[phone] = results[phone]

                    # 检查是否需要自动兑换
                    if CONFIG['AUTO_EXCHANGE']:
                        self.auto_exchange(phone, available)

                    logger.info(f"账号 {phone[:3]}***{phone[7:]} 查询完成")

                    # 更新成功计数
                    with query_lock:
                        if query_id in query_status:
                            query_status[query_id]['success_count'] += 1
                else:
                    logger.error(f"账号 {phone[:3]}***{phone[7:]} 登录失败")
                    # 即使登录失败也记录结果，避免数据丢失
                    results[phone] = {
                        'total': 0,
                        'available': 0,
                        'vouchers': '登录失败',
                        'tasks': [],
                        'last_query': get_current_shanghai_time()
                    }
                    self.results[phone] = results[phone]

                    # 更新失败计数
                    with query_lock:
                        if query_id in query_status:
                            query_status[query_id]['fail_count'] += 1

            except Exception as e:
                logger.error(f"账号 {phone[:3]}***{phone[7:]} 查询异常: {e}")
                # 记录异常结果
                results[phone] = {
                    'total': 0,
                    'available': 0,
                    'vouchers': f'查询异常: {str(e)}',
                    'tasks': [],
                    'last_query': get_current_shanghai_time()
                }
                self.results[phone] = results[phone]

                # 更新失败计数
                with query_lock:
                    if query_id in query_status:
                        query_status[query_id]['fail_count'] += 1

            # 更新已处理账号数（查询完成后）
            progress = int((index / total_phones) * 85) + 10  # 10-95%的进度范围
            with query_lock:
                if query_id in query_status:
                    query_status[query_id].update({
                        'progress': progress,
                        'message': f'已完成第 {index}/{total_phones} 个账号查询',
                        'processed_accounts': index,
                        'total_accounts': total_phones
                    })

            # 避免请求过快，给服务器一些缓冲时间
            time.sleep(2)

        success_count = len([r for r in results.values() if r.get('total', 0) > 0])
        fail_count = len([r for r in results.values() if r.get('total', 0) == 0])
        logger.info(f"所有账号查询完成，成功 {success_count} 个，失败 {fail_count} 个")

        # 设置最终完成状态
        with query_lock:
            if query_id in query_status:
                query_status[query_id].update({
                    'progress': 100,
                    'message': f'查询完成！成功 {success_count} 个，失败 {fail_count} 个',
                    'processed_accounts': total_phones,
                    'total_accounts': total_phones,
                    'status': 'completed'
                })

        # 只更新内存数据，不保存到文件
        self.data.update(results)

        return results

    # 自动兑换逻辑
    def auto_exchange(self, phone, available):
        thresholds = CONFIG['EXCHANGE_THRESHOLDS']
        exchange_success = False
        
        for threshold in thresholds:
            if available >= threshold:
                logger.info(f"账号 {phone[:3]}***{phone[7:]} 余额 {available}元 >= {threshold}元，尝试兑换")
                
                # 严格按照阈值决定兑换金额，只兑换阈值明确指定的额度
                # 5.0 -> 兑换5元, 2.0 -> 兑换2元，不存在自动选择接近的情况
                if threshold == 5.0:
                    result = self.dh5r(phone)
                    amount = 5.0
                elif threshold == 2.0:
                    result = self.dh2r(phone)
                    amount = 2.0
                else:
                    # 未知阈值，跳过
                    logger.warning(f"未知兑换阈值: {threshold}，跳过此阈值")
                    continue
                    
                # 记录兑换结果
                if "成功" in result:
                    exchange_success = True
                    if phone not in self.data:
                        self.data[phone] = {}
                    if 'exchange_history' not in self.data[phone]:
                        self.data[phone]['exchange_history'] = []
                    self.data[phone]['exchange_history'].append({
                        'time': get_current_shanghai_time(),
                        'amount': amount,
                        'result': result
                    })
                    save_data(self.data, self.data_file)
                break
        
        # 如果兑换成功，重新查询话费券信息
        if exchange_success:
            time.sleep(1)  # 等待1秒确保服务器数据已更新
            # 重新查询余额和话费券
            _, available = self.userinfo(phone)
            vouchers = self.hfq(phone)
            # 更新结果
            if phone in self.results:
                self.results[phone]['available'] = available
                self.results[phone]['vouchers'] = vouchers

    # 打印结果
    def print_results(self):
        # 获取当前时间
        current_time = get_current_shanghai_time()

        html = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联通话费查询结果</title>
    <link rel="icon" href="data:,">
    <style>
        body {
            font-family: "Microsoft YaHei", sans-serif;
            margin: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .query-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        .query-button:hover {
            background-color: #0056b3;
        }
        .query-button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status-message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        table {
            border-collapse: collapse;
            width: 100%;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border: 1px solid #ddd;
            vertical-align: top;
        }
        th {
            background-color: #f8f9fa;
            font-weight: normal;
        }
        .time-display {
            text-align: center;
            margin: 10px auto 20px;
            max-width: 300px;
        }
        .time-box {
            border: 1px solid #ff0000;
            padding: 8px;
            display: inline-block;
            min-width: 200px;
        }

        /* 密码弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border: none;
            border-radius: 10px;
            width: 300px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        .modal-header {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .modal-input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            text-align: center;
            margin-bottom: 15px;
            box-sizing: border-box;
        }
        .modal-input:focus {
            outline: none;
            border-color: #007bff;
        }
        .modal-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        .modal-btn {
            padding: 8px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .modal-btn-ok {
            background-color: #007bff;
            color: white;
        }
        .modal-btn-ok:hover {
            background-color: #0056b3;
        }
        .modal-btn-cancel {
            background-color: #6c757d;
            color: white;
        }
        .modal-btn-cancel:hover {
            background-color: #545b62;
        }

        /* 查询进度条 */
        .progress-container {
            width: 100%;
            background-color: #f0f0f0;
            border-radius: 5px;
            margin: 10px 0;
            display: none;
        }
        .progress-bar {
            width: 0%;
            height: 20px;
            background-color: #007bff;
            border-radius: 5px;
            text-align: center;
            line-height: 20px;
            color: white;
            font-size: 12px;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="header">
        <button class="query-button" onclick="showPasswordModal()">手动查询</button>
        <div id="statusMessage" class="status-message"></div>
        <div class="progress-container" id="progressContainer">
            <div class="progress-bar" id="progressBar">准备查询...</div>
        </div>
    </div>

    <!-- 密码弹窗 -->
    <div id="passwordModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">请输入查询密码</div>
            <input type="password" id="passwordInput" class="modal-input" placeholder="请输入密码" maxlength="10">
            <div class="modal-buttons">
                <button class="modal-btn modal-btn-ok" onclick="confirmPassword()">确认</button>
                <button class="modal-btn modal-btn-cancel" onclick="closePasswordModal()">取消</button>
            </div>
        </div>
    </div>
    <div class="time-display">
        <div style="font-size: 12px; color: #666; margin-bottom: 5px;">最后查询时间</div>
        <div class="time-box" id="timeBox">
            """ + current_time + """
        </div>
    </div>
    <div id="tableContainer">
    <table>
        <thead>
            <tr>
                <th>手机号</th>
                <th>金额</th>
                <th>任务状态</th>
                <th>待使用话费券</th>
            </tr>
        </thead>
        <tbody id="tableBody">"""
        
        for phone, data in self.results.items():
            masked_phone = f"{phone[:3]}***{phone[7:]}"
            available = data['available']
            
            # 生成任务状态HTML
            tasks_html = ""
            for task in data['tasks']:
                tasks_html = f"阅读{task['current']}分钟: {task['current']}/{task['total']}"
            
            # 处理话费券显示
            vouchers_display = ""
            if data['vouchers']:
                vouchers_lines = data['vouchers'].split('\n')
                # 移除最后一行的总金额显示
                if vouchers_lines and '总未使用金额' in vouchers_lines[-1]:
                    total_amount = vouchers_lines[-1].split(': ')[1].replace('元', '')
                    vouchers_lines = vouchers_lines[:-1]
                    vouchers_display = '<br>'.join(vouchers_lines)
                    if vouchers_display:
                        vouchers_display += f'<br>总未使用金额: {total_amount}'
            
            # 生成表格行
            html += f"""
            <tr>
                <td>{masked_phone}</td>
                <td>{available}</td>
                <td>{tasks_html}</td>
                <td>{vouchers_display}</td>
            </tr>"""
        
        html += """
        </tbody>
    </table>
    </div>

    <script>
        function showStatus(message, type) {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.textContent = message;
            statusDiv.className = 'status-message status-' + type;
            statusDiv.style.display = 'block';

            // 自动隐藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000); // 增加到5秒
            }
        }

        function updateTime() {
            // 这个函数现在只在查询完成时调用，更新为查询执行时间
            const now = new Date();
            const timeString = now.getFullYear() + '-' +
                String(now.getMonth() + 1).padStart(2, '0') + '-' +
                String(now.getDate()).padStart(2, '0') + ' ' +
                String(now.getHours()).padStart(2, '0') + ':' +
                String(now.getMinutes()).padStart(2, '0') + ':' +
                String(now.getSeconds()).padStart(2, '0');
            document.getElementById('timeBox').textContent = timeString;
        }

        // 显示密码弹窗
        function showPasswordModal() {
            document.getElementById('passwordModal').style.display = 'block';
            document.getElementById('passwordInput').focus();
        }

        // 关闭密码弹窗
        function closePasswordModal() {
            document.getElementById('passwordModal').style.display = 'none';
            document.getElementById('passwordInput').value = '';
        }

        // 确认密码
        function confirmPassword() {
            const password = document.getElementById('passwordInput').value;

            // 验证密码（后端验证，前端不暴露密码）
            if (!password) {
                showStatus('请输入密码！', 'error');
                return;
            }

            closePasswordModal();
            manualQuery(password);
        }

        // 手动查询函数
        function manualQuery(password) {
            // 禁用按钮，显示查询中状态
            const button = document.querySelector('.query-button');
            button.disabled = true;
            button.textContent = '查询中...';

            // 显示进度条
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            progressContainer.style.display = 'block';
            progressBar.style.width = '5%';
            progressBar.textContent = '验证密码...';

            showStatus('正在启动查询，请稍候...', 'info');

            // 发送查询请求（包含密码）- 增加超时时间
            fetch('/api/query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({password: password}),
                // 不设置超时，让浏览器使用默认超时
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.query_id) {
                    // 查询已启动，开始轮询状态
                    showStatus('查询已启动，正在获取进度...', 'info');
                    progressBar.textContent = '查询已启动...';
                    progressBar.style.width = '10%';

                    // 开始轮询查询状态
                    pollQueryStatus(data.query_id, data.total_accounts);
                } else {
                    progressContainer.style.display = 'none';
                    if (data.message && data.message.includes('密码')) {
                        showStatus('密码错误！', 'error');
                    } else {
                        showStatus('查询启动失败：' + (data.message || '未知错误'), 'error');
                    }
                    // 恢复按钮状态
                    button.disabled = false;
                    button.textContent = '手动查询';
                }
            })
            .catch(error => {
                console.error('查询启动错误:', error);
                progressContainer.style.display = 'none';
                showStatus('查询启动失败：网络连接错误', 'error');
                // 恢复按钮状态
                button.disabled = false;
                button.textContent = '手动查询';
            });
        }

        // 轮询查询状态
        function pollQueryStatus(queryId, totalAccounts) {
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const button = document.querySelector('.query-button');

            let pollCount = 0;
            const maxPolls = 300; // 最多轮询5分钟（每秒一次）

            const pollInterval = setInterval(() => {
                pollCount++;

                // 检查是否超过最大轮询次数
                if (pollCount > maxPolls) {
                    clearInterval(pollInterval);
                    progressContainer.style.display = 'none';
                    showStatus('查询超时，请刷新页面查看结果', 'error');
                    button.disabled = false;
                    button.textContent = '手动查询';
                    return;
                }

                // 发送状态查询请求
                fetch('/api/query_status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({query_id: queryId})
                })
                .then(response => response.json())
                .then(statusData => {
                    if (statusData.success) {
                        const status = statusData.status;
                        const progress = statusData.progress;
                        const message = statusData.message;
                        const processedAccounts = statusData.processed_accounts;
                        const successCount = statusData.success_count;
                        const failCount = statusData.fail_count;

                        // 更新进度条
                        progressBar.style.width = progress + '%';

                        // 更新进度文本
                        if (processedAccounts !== undefined && totalAccounts) {
                            progressBar.textContent = `查询进度: ${processedAccounts}/${totalAccounts} (${progress}%)`;
                        } else {
                            progressBar.textContent = message || `进度: ${progress}%`;
                        }

                        // 更新状态消息
                        let statusMessage = message;
                        if (processedAccounts !== undefined && totalAccounts) {
                            statusMessage += ` (${processedAccounts}/${totalAccounts})`;
                        }
                        if (successCount !== undefined && failCount !== undefined) {
                            statusMessage += ` 成功:${successCount} 失败:${failCount}`;
                        }
                        showStatus(statusMessage, 'info');

                        // 检查是否完成
                        if (status === 'completed') {
                            clearInterval(pollInterval);

                            // 获取查询结果
                            if (statusData.results) {
                                const results = statusData.results;

                                // 更新表格内容
                                if (results.tableContent) {
                                    document.getElementById('tableBody').innerHTML = results.tableContent;
                                }

                                // 更新查询时间
                                if (results.timestamp) {
                                    document.getElementById('timeBox').textContent = results.timestamp;
                                }

                                // 显示完成消息
                                let completedMessage = `查询完成！`;
                                if (results.queryTime) {
                                    completedMessage += ` 耗时 ${results.queryTime.toFixed(1)} 秒`;
                                }
                                if (results.totalAccounts) {
                                    completedMessage += `，共 ${results.totalAccounts} 个账号`;
                                    if (results.successCount !== undefined && results.failCount !== undefined) {
                                        completedMessage += `，成功 ${results.successCount} 个，失败 ${results.failCount} 个`;
                                    }
                                }
                                showStatus(completedMessage, 'success');

                                // 进度条显示完成
                                progressBar.style.width = '100%';
                                progressBar.textContent = '查询完成';

                                // 隐藏进度条
                                setTimeout(() => {
                                    progressContainer.style.display = 'none';
                                }, 3000);
                            }

                            // 恢复按钮状态
                            button.disabled = false;
                            button.textContent = '手动查询';

                        } else if (status === 'failed') {
                            clearInterval(pollInterval);
                            progressContainer.style.display = 'none';
                            showStatus('查询失败：' + message, 'error');

                            // 恢复按钮状态
                            button.disabled = false;
                            button.textContent = '手动查询';
                        }
                        // 如果状态是 'starting' 或 'running'，继续轮询

                    } else {
                        // 状态查询失败
                        console.warn('状态查询失败:', statusData.message);
                        // 继续轮询，可能是临时网络问题
                    }
                })
                .catch(error => {
                    console.warn('状态查询网络错误:', error);
                    // 继续轮询，可能是临时网络问题
                });

            }, 1000); // 每秒轮询一次
        }

        // 键盘事件处理
        document.addEventListener('keydown', function(event) {
            const modal = document.getElementById('passwordModal');
            if (modal.style.display === 'block') {
                if (event.key === 'Enter') {
                    confirmPassword();
                } else if (event.key === 'Escape') {
                    closePasswordModal();
                }
            }
        });

        // 时间显示现在只在查询时更新，不再自动更新
    </script>
</body>
</html>"""
        
        # 保存HTML文件
        try:
            with open('ltcx_results.html', 'w', encoding='utf-8') as f:
                f.write(html)
            print("结果已保存到 ltcx_results.html")
        except Exception as e:
            logger.error(f"保存HTML结果失败: {e}")
        
        # 同时在控制台显示基本信息
        print("\n===== 联通账号余额查询结果 =====")
        for phone, data in self.results.items():
            masked_phone = f"{phone[:3]}***{phone[7:]}"
            available = data['available']
            
            print(f"\n【手机号】{masked_phone}")
            print(f"【当前话费红包】{available}元")
            
            if data['vouchers']:
                print("【待使用话费券】")
                print(data['vouchers'].rstrip())
            
            print("【任务状态】")
            for task in data['tasks']:
                print(f"  阅读{task['current']}分钟: {task['current']}/{task['total']}")
            
            if phone in self.data and 'exchange_history' in self.data[phone]:
                histories = self.data[phone]['exchange_history']
                recent_histories = histories[-3:] if len(histories) > 3 else histories
                print("【兑换历史】")
                for history in recent_histories:
                    time_str = history.get('time', '')
                    amount = history.get('amount', '')
                    result = history.get('result', '')
                    print(f"  {time_str} 兑换{amount}元: {result}")

    # 生成表格行的HTML内容
    def generate_table_rows(self):
        table_content = ""
        for phone, data in self.results.items():
            masked_phone = f"{phone[:3]}***{phone[7:]}"
            available = data['available']

            # 生成任务状态HTML
            tasks_html = ""
            for task in data['tasks']:
                tasks_html = f"阅读{task['current']}分钟: {task['current']}/{task['total']}"

            # 处理话费券显示
            vouchers_display = ""
            if data['vouchers']:
                vouchers_lines = data['vouchers'].split('\n')
                # 移除最后一行的总金额显示
                if vouchers_lines and '总未使用金额' in vouchers_lines[-1]:
                    total_amount = vouchers_lines[-1].split(': ')[1].replace('元', '')
                    vouchers_lines = vouchers_lines[:-1]
                    vouchers_display = '<br>'.join(vouchers_lines)
                    if vouchers_display:
                        vouchers_display += f'<br>总未使用金额: {total_amount}'

            # 生成表格行
            table_content += f"""
            <tr>
                <td>{masked_phone}</td>
                <td>{available}</td>
                <td>{tasks_html}</td>
                <td>{vouchers_display}</td>
            </tr>"""

        return table_content

    # 将内部数据结构转换为前端期望的JSON格式
    def convert_to_json_results(self):
        """
        将内部results数据转换为前端QueryResult[]格式
        """
        json_results = []

        for phone, data in self.results.items():
            # 脱敏手机号
            masked_phone = f"{phone[:3]}***{phone[7:]}"

            # 获取金额
            amount = data.get('available', 0)

            # 处理任务状态
            status = "成功" if data.get('total', 0) > 0 else "失败"
            if data.get('vouchers') == '登录失败':
                status = "登录失败"
            elif '查询异常' in str(data.get('vouchers', '')):
                status = "查询异常"

            # 计算话费券数量
            coupon_count = 0
            if data.get('vouchers') and data.get('vouchers') != '登录失败':
                vouchers_str = str(data['vouchers'])
                if '查询异常' not in vouchers_str:
                    # 计算话费券行数（排除总金额行）
                    voucher_lines = vouchers_str.split('\n')
                    coupon_count = len([line for line in voucher_lines if line.strip() and '总未使用金额' not in line])

            # 生成备注信息
            remark = ""
            if data.get('tasks'):
                task_info = []
                for task in data['tasks']:
                    task_info.append(f"阅读{task['current']}/{task['total']}分钟")
                remark = "; ".join(task_info)

            if data.get('vouchers') and data.get('vouchers') != '登录失败':
                vouchers_str = str(data['vouchers'])
                if '查询异常' not in vouchers_str:
                    # 提取总金额信息
                    voucher_lines = vouchers_str.split('\n')
                    for line in voucher_lines:
                        if '总未使用金额' in line:
                            total_amount = line.split(': ')[1].replace('元', '') if ': ' in line else ''
                            if total_amount:
                                if remark:
                                    remark += f"; 话费券总额: {total_amount}元"
                                else:
                                    remark = f"话费券总额: {total_amount}元"
                            break

            # 创建QueryResult对象
            result = {
                'phone': masked_phone,
                'amount': float(amount) if amount else 0.0,
                'status': status,
                'coupon': coupon_count,
                'success': data.get('total', 0) > 0,
                'queryTime': data.get('last_query', ''),
                'remark': remark if remark else None
            }

            json_results.append(result)

        return json_results

# HTTP服务器相关代码已移除，现在统一使用server.py的2032端口
# 保留查询状态管理，供server.py使用
import threading
from threading import Lock

# 全局查询状态管理
query_status = {}
query_lock = Lock()

# QueryHandler类已移除，HTTP服务器功能现在由server.py统一处理

# 静态文件服务相关方法已移除，现在由server.py统一处理

# SPA和后备页面服务相关方法已移除，现在由server.py统一处理

# 所有HTTP服务器相关代码已移除，现在由server.py统一处理

def node_integration():
    """Node.js集成函数
    此函数用于从Node.js调用此脚本，提供与Node.js的无缝集成
    
    使用示例:
    ```javascript
    const { exec } = require('child_process');
    
    async function fetchCKFromFile(url) {
      try {
        const response = await fetch(url);
        const text = await response.text();
        return text;
      } catch (error) {
        console.error('获取远程CK失败:', error);
        throw error;
      }
    }
    
    async function runUnicomCheck() {
      let CK;
      try {
        CK = await fetchCKFromFile('https://alist.201626.xyz/d/%E8%8B%A5%E5%AF%92/1/ck.txt?sign=bRLggerXCz6_Ld64ADyaqkHgwEgzimYQn7J8MimZhCo=:0');
        process.env.zgltck1 = CK;
      } catch (error) {
        console.error('获取 CK 变量内容失败:', error);
        // 远程失败时使用本地 CK
        process.env.zgltck1 = localCKs.join('\n');
      }
      
      // 如果远程部分获取失败，则使用本地 CK
      if (!CK) {
        process.env.zgltck1 = localCKs.join('\n');
      }
      
      // 运行Python脚本
      exec('python ltcx.py --auto', (error, stdout, stderr) => {
        if (error) {
          console.error(`执行错误: ${error}`);
          return;
        }
        console.log(`输出: ${stdout}`);
        if (stderr) console.error(`错误: ${stderr}`);
      });
    }
    ```
    """
    pass

def main():
    parser = argparse.ArgumentParser(description='联通查询兑换脚本')
    parser.add_argument('-q', '--query', action='store_true', help='查询所有手机号')
    parser.add_argument('-e', '--exchange', choices=['5', '2', 'auto'], help='兑换红包(5元/2元/自动)')
    parser.add_argument('-t', '--threshold', type=float, help='设置兑换阈值(单个值，将用作唯一阈值)')
    parser.add_argument('-d', '--duihuan', help='设置兑换阈值数组，格式如"5,2"表示[5,2]')
    parser.add_argument('-r', '--remote', help='从远程URL获取手机号列表')
    parser.add_argument('-a', '--auto', action='store_true', help='自动模式(查询+根据阈值自动兑换)')
    # 移除HTTP服务器相关参数，现在统一使用server.py的2032端口
    args = parser.parse_args()
    
    # 如果指定了远程URL，更新配置
    if args.remote:
        CONFIG['REMOTE_URL'] = args.remote
    elif os.getenv('REMOTE_URL'):
        CONFIG['REMOTE_URL'] = os.getenv('REMOTE_URL')
    
    # 获取手机号列表
    try:
        phone_numbers = get_phone_numbers()
        if not phone_numbers:
            logger.error("无法获取有效的手机号列表")
            return
        
        if len(phone_numbers) == 0:
            logger.error("手机号列表为空")
            return
            
        logger.info(f"成功获取到 {len(phone_numbers)} 个手机号")
    except Exception as e:
        logger.error(f"获取手机号列表失败: {e}")
        return
    
    # 处理兑换阈值设置
    if args.duihuan:
        try:
            thresholds = [float(x.strip()) for x in args.duihuan.split(',')]
            if thresholds:
                CONFIG['EXCHANGE_THRESHOLDS'] = thresholds
                logger.info(f"设置兑换阈值数组为: {thresholds}")
        except Exception as e:
            logger.error(f"解析兑换阈值数组失败: {e}")
    elif args.threshold:
        CONFIG['EXCHANGE_THRESHOLDS'] = [args.threshold]
        logger.info(f"设置兑换阈值为: {args.threshold}")
    elif os.getenv('EXCHANGE_THRESHOLDS'):
        try:
            CONFIG['EXCHANGE_THRESHOLDS'] = json.loads(os.getenv('EXCHANGE_THRESHOLDS'))
            logger.info(f"从环境变量获取兑换阈值: {CONFIG['EXCHANGE_THRESHOLDS']}")
        except Exception as e:
            logger.error(f"解析环境变量兑换阈值失败: {e}")
    
    # 创建LT实例
    lt = LT()
    
    # 自动模式
    if args.auto:
        CONFIG['AUTO_EXCHANGE'] = True
        lt.query_all(phone_numbers)
        lt.print_results()
        return
    
    # 根据参数执行操作
    if args.query or not (args.query or args.exchange):
        lt.query_all(phone_numbers)
        lt.print_results()
    
    # 处理兑换请求
    if args.exchange:
        for phone in phone_numbers:
            if lt.login(phone):
                _, available = lt.userinfo(phone)
                
                if args.exchange == '5':
                    logger.info(f"手动请求兑换5元红包: {phone[:3]}***{phone[7:]}")
                    result = lt.dh5r(phone)
                    print(f"手机号 {phone[:3]}***{phone[7:]} 兑换5元结果: {result}")
                    if "成功" in result:
                        if phone not in lt.data:
                            lt.data[phone] = {}
                        if 'exchange_history' not in lt.data[phone]:
                            lt.data[phone]['exchange_history'] = []
                        lt.data[phone]['exchange_history'].append({
                            'time': get_current_shanghai_time(),
                            'amount': 5,
                            'result': result
                        })
                        save_data(lt.data, lt.data_file)
                
                elif args.exchange == '2':
                    logger.info(f"手动请求兑换2元红包: {phone[:3]}***{phone[7:]}")
                    result = lt.dh2r(phone)
                    print(f"手机号 {phone[:3]}***{phone[7:]} 兑换2元结果: {result}")
                    if "成功" in result:
                        if phone not in lt.data:
                            lt.data[phone] = {}
                        if 'exchange_history' not in lt.data[phone]:
                            lt.data[phone]['exchange_history'] = []
                        lt.data[phone]['exchange_history'].append({
                            'time': get_current_shanghai_time(),
                            'amount': 2,
                            'result': result
                        })
                        save_data(lt.data, lt.data_file)
                
                elif args.exchange == 'auto':
                    lt.auto_exchange(phone, available)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常: {e}", exc_info=True) 