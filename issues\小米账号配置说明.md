# 小米金融多账号查询工具配置说明

## 功能特点

✅ **多账号支持** - 支持配置多个小米账号同时查询  
✅ **交互式选择** - 运行时可选择查询单个或所有账号  
✅ **命令行参数** - 支持通过参数直接指定账号  
✅ **详细输出** - 显示完整的响应内容和格式化结果  
✅ **错误处理** - 完善的异常处理和状态反馈  

## 使用方法

### 1. 交互式运行
```bash
python 小米.PY
```
程序会显示可用账号列表，您可以选择：
- 查询单个账号
- 查询所有账号
- 退出程序

### 2. 命令行参数
```bash
# 查询指定账号
python 小米.PY 主账号

# 查询所有账号
python 小米.PY all
```

## Cookie配置格式

在 `小米.PY` 文件中找到 `ACCOUNTS` 字典，按以下格式添加账号：

```python
ACCOUNTS = {
    "主账号": "passToken=完整的passToken值; userId=用户ID;",
    "小号1": "passToken=另一个完整的passToken值; userId=另一个用户ID;",
    "小号2": "passToken=第三个完整的passToken值; userId=第三个用户ID;",
    # 继续添加更多账号...
}
```

## 如何获取Cookie

### 方法1：浏览器开发者工具
1. 打开小米金融网页并登录
2. 按F12打开开发者工具
3. 切换到Network标签
4. 刷新页面或进行操作
5. 找到请求头中的Cookie字段
6. 复制 `passToken` 和 `userId` 的值

### 方法2：手机抓包
1. 使用抓包工具（如Charles、Fiddler）
2. 配置手机代理
3. 在小米钱包APP中进行操作
4. 查看请求头获取Cookie信息

## Cookie格式示例

```
passToken=V1:DXmurwq2/R1BHTELu6obCesS3kzcLLWi0s7UW8UfGKmiQoGMffU9KleB8JDhA+xp6Y+zP2exneIit4U52YeSAEsNNi+Het+IFsXW8QCXUOPxyNkqYZ/KZB1/JTVX4M0wEzKa7LQsvQXz5fNS3pTHPcQ+J1v6AjRzBRHxHW9KVRI4YKN3d7Zg+tg95Kztxo2c/DM2XOdqZK6R5lTbJ/AEE+UxzdNjIuRvJoglhW3EST/7BGDBwiJpmQLXHAVXyLJ3dypSb4yWQCjSLPBIQxfA6Klb0qM/1kv7vK0tEFEi/43MxTeB4/5shMU8vVfQcxOI3pzZgy00OBBSQRwdAEDu8g==; userId=1217497311;
```

## 注意事项

⚠️ **Cookie安全性**
- Cookie包含敏感信息，请妥善保管
- 不要将包含真实Cookie的文件上传到公共代码仓库
- 定期更新Cookie以确保有效性

⚠️ **账号管理**
- 未配置的账号（包含"xxx"的）会被自动跳过
- 建议为每个账号设置有意义的名称
- 可以随时添加或删除账号配置

⚠️ **使用限制**
- 请遵守小米服务条款
- 避免频繁请求导致账号被限制
- 建议合理控制查询频率

## 输出说明

程序会显示以下信息：
- 登录状态码
- 查询状态码  
- 原始响应内容
- 格式化JSON数据
- 金币余额统计
- 错误信息（如有）

## 故障排除

### 常见问题

**1. 显示"页面不存在"**
- 检查Cookie是否有效
- 确认passToken和userId格式正确

**2. 登录失败**
- Cookie可能已过期，需要重新获取
- 检查网络连接

**3. 查询结果为0**
- 账号可能没有金币余额
- 或者Cookie无效

**4. JSON解析错误**
- 响应格式可能发生变化
- 检查API是否正常

### 调试建议
- 查看完整的响应内容
- 检查HTTP状态码
- 验证Cookie格式和有效性
