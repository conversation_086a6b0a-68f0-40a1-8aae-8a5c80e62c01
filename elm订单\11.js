const CryptoJS = require('crypto-js');

const AES_KEY = "xifan_kwaix_2024";

function generateIV() {
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    let iv = '';
    for (let i = 0; i < 16; i++) {
        iv += chars[Math.floor(Math.random() * chars.length)];
    }
    return iv;
}

function encrypt(plaintext, keyStr, ivStr) {
    const key = CryptoJS.enc.Utf8.parse(keyStr);
    const iv = CryptoJS.enc.Utf8.parse(ivStr);
    const encrypted = CryptoJS.AES.encrypt(plaintext, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    return encrypted.ciphertext.toString(CryptoJS.enc.Base64);
}

function decrypt(encryptedBase64, keyStr, ivStr) {
    const key = CryptoJS.enc.Utf8.parse(keyStr);
    const iv = CryptoJS.enc.Utf8.parse(ivStr);
    const cipherText = CryptoJS.enc.Base64.parse(encryptedBase64);
    const decrypted = CryptoJS.AES.decrypt(
        { ciphertext: cipherText },
        key,
        { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 }
    );
    return decrypted.toString(CryptoJS.enc.Utf8);
}

// 测试
const json = {aHoKsnUILeIwL9lfadORYJUTsUnwGHC2FtSQsqHzE1gJVKrcpFnTEhIJjMeKBWAGmBW7220Ix4RJxKE7Jzmj2lvjoMdqQkb6SDjCRJ3p1gXmFEJeiqKVA6OR21WxYWIOsLuuskJ/1/HgdPlIJi/BAHFwdOzAcNEXgWbzjguARGa/f6HkyUQXurLI1U2lFYmmcvHVh32CCICziw4bToC4lhWyvH27PShd/ChTYGqq8jBHg/Gv0CloFxrR0Db7FPFLDfARkA8fna05bqGwSRTcZVCd6HArB/rcuE7vXL5UdY33+XpJfRXtWA2k4CHREG7X2Ouq7r5KRFNEsZ7RbWow7/WSqjcLdF/AIOcCEZMj967hgu/rHDpR8NQHXXl42KJPj/CA4WZg7WzYP1X26cf5fJf1XDyA0BcAFhwURnP+FoT2xmexoiymyKX1csM+3OF5H2TCiJQPhPy02qcCSM32pqzG+th2OKSXlYB0NOAhqSUkYZQ0Wj+JtuBBZCDMF1+rvQJPkZVog8R9DwXJg3teBmEbSawCoufhZEHBnJjsUlwI8YHWni8TyWvrDRQrZXcFHQK+lVpppso8tDEGgzv0MEATeVtihxOMEHf9nVF0VvkBvBYCp7DGONQXYzC6F9YrSUDBZl7O5g9X0jNnzjMrCgYkP2evjFbAYKTw+4I0EcxxVjh987nkIEuZy5DmiyREkSxNQa5vAjGt5mLXOqDOQGikSLewSGqLDPwEd+AdP/TMD+wkfsdODDr3lePq9Ob88lg8FSLlEgsA8E+Y2CGK7oVO7Jd04epB2GCDPXo6Jcetmwa6XIH9VbT5ndaHaesQzxAcxLESW21ZtmWq7HJyYUnfzGVdim3O3d0DDysnKRDAUaPOUWzv72F0IBgHDrOv/1fkGV+OcFUG29y2t8vX4xnNVlly/QUMS2y8sifFeY4aCkBjLI1m55ETFgJJwGIzUvY2PqygXB3ARoSaApJbea0CvInQSUXopgqUgE0AWIr6NXzNLzEjGskYhx0CE/3n+CyvaTDW2IOH3N90tzaAtmTb69DYTvZfRrgwxr5kmZjz7NG1hTYYuiKdL6lL1ZI5BQQVATAng2SxYoMw8C+rQjIXkVdcQjrgOu9FSliamiIIWRO8K78jPN36LPsUm5YC/xSggy8pBQU+oikztx7Wmzdg/5II8hVw0WyjQNPxcbiYbPaiOTEw59vvueYnvepuTfarpb3qPJ+39aORoTlPaEm0ZK5zqf8oJoQXRZgMkAXMio7n7sea+zuyR6wqCzAS6a5vGeYJvhKzsooE5khBPrEmP8hL2KcSVS1lpdCNJB3cShkAyz6+ERJ923ftVXHvQyC2Zr3th5ypU+psNmCAaKe1EJW13Py7SiGy5s876XxDDVDpmSx0sjI36sJ2/HoreItaqxnSJsm4PerRQ/Sy1/j3SIWl9MiO8rHYst5xTQTeUStpAXBcSUv626eLhQnHnrpOd2aUwkfCQNGv792E8WOOjTf+7KWbEJtXG5UfH4brDvHS1Z3vML5Zr7p4pLjUxuTU0SzROdcfK2FjKUwIoCubB1P8TajAn0tpuKQz2h8K76J15R0hk1ILg7qnOTrxXR9m8GXq89SnktzOCcsIMMCjiASjp90nDVCEKR9hfcvUHDPuaRPrB4USO5gLhATUqR/WJlpFx3YQMPBgOFqVtlozR9qLGu/i1ttSTdiNQhyaX8Jsq8By3vXwjoqF6P09780xxue7tUtG31MZ0k2nLtRlU7MVjIp3+fM9FoAdyifNtSfBBr4A5tfomapxBlgxqqKsc0XsdahCz7ku3LAya8lum2HFpeu84wmEqwRRE0NFWLUgHiLCDwWdqRyZY5m2M4FhAFrlYiNCM6Izz8UsWGj7Q7Ik9oBqvwt15gKR3Dk=;}
const iv = generateIV();
const data = encrypt(JSON.stringify(json), AES_KEY, iv);
const result = decrypt(data, AES_KEY, iv);

console.log("明文:", json);
console.log("密文:", data);
console.log("解密:", result);