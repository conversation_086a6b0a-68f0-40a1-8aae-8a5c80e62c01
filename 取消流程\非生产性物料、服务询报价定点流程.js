// 非生产性物料、服务询报价定点流程 - 优化版
(function() {
    // 配置区 - 统一管理字段和表格
    const CONFIG = {
        // 明细表配置
        detailTables: ['xmxxmxb_845065503573', 'ft_1061832_fwxxmxb'],
        detailFields: ['je1', 'zjfd', 'zb1', 'zb2', 'zb3'], // WeFormSDK绑定字段
        
        // DOM操作字段
        domFields: ['yin1'], // 涨价幅度字段
        
        // 动态字段生成
        get yarnFields() { 
            return Array.from({length: 30}, (_, i) => `yarn${i + 1}`); 
        },
        get supplierFields() { 
            return Array.from({length: 30}, (_, i) => `supplier${i + 1}`); 
        },
        get supplierDescFields() { 
            return Array.from({length: 30}, (_, i) => `gysms${i + 1}`); 
        },
        
        // DOM表格
        domTables: ['table1', 'table2'],
        
        // 所有需要监听的字段
        get allFields() { 
            return [...this.detailFields, ...this.domFields, ...this.yarnFields]; 
        }
    };

    // 全局变量保持兼容性
    var flagHideArr = [];
    var fieldValueArr = [];

    // 核心颜色设置方法
    const setFieldColor = (field, value, fieldType) => {
        if (!field) return;
        
        switch(fieldType) {
            case 'price': // 涨价幅度
                const numValue = parseFloat(value);
                field.style.backgroundColor = numValue > 0 ? "#E1FAEF" : "";
                break;
            case 'standard': // 标准字段
                field.style.backgroundColor = value ? "#E1FAEF" : "";
                break;
            case 'yarn': // 中标字段
                const yarnNum = field.getAttribute('id').replace('yarn', '');
                updateSupplierBackground(field, value.trim(), yarnNum);
                break;
        }
    };

    // 供应商背景更新方法
    const updateSupplierBackground = (field, value, supplierNum) => {
        const row = field.closest('tr');
        if (row) {
            const supplierElement = row.querySelector('#color_supplier' + supplierNum);
            if (supplierElement) {
                supplierElement.style.backgroundColor = value ? "#E1FAEF" : "";
            }
        }
    };

    // 供应商标签更新方法 - 增强版，支持放大表格
    const updateSupplierLabel = (supplierNum, value) => {
        // 方法1：通过配置的表格ID查找
        CONFIG.domTables.forEach(tableId => {
            const table = document.getElementById(tableId);
            if (table) {
                updateSupplierInTable(table, supplierNum, value);
            }
        });
        
        // 方法2：全局查找所有supplier元素（处理放大表格等动态场景）
        const allSupplierElements = document.querySelectorAll('[id="supplier' + supplierNum + '"]');
        allSupplierElements.forEach(element => {
            updateSupplierElement(element, supplierNum, value);
        });
    };

    // 在指定表格中更新供应商标签
    const updateSupplierInTable = (table, supplierNum, value) => {
        const supplierElements = table.querySelectorAll('#supplier' + supplierNum);
        supplierElements.forEach(element => {
            updateSupplierElement(element, supplierNum, value);
        });
    };

    // 更新单个供应商元素
    const updateSupplierElement = (element, supplierNum, value) => {
        const spans = element.querySelectorAll('span');
        spans.forEach(span => {
            if (value) {
                span.textContent = value + ' ';
                // 代理报价红色标记
                if (value.includes('代理报价')) {
                    span.style.color = 'red';
                    span.style.fontWeight = 'bold';
                } else {
                    span.style.color = "";
                    span.style.fontWeight = "";
                }
            } else {
                span.textContent = '供应商' + supplierNum;
                span.style.color = "";
                span.style.fontWeight = "";
            }
        });
    };

    // 初始化所有字段颜色
    const initAllFieldColors = () => {
        // 处理涨价幅度字段
        document.querySelectorAll('[id="yin1"]').forEach(field => {
            const input = field.querySelector("input") || field.querySelector("span") || field;
            const value = input.textContent || input.value || "";
            setFieldColor(field, value, 'price');
        });

        // 处理中标字段
        CONFIG.yarnFields.forEach(fieldId => {
            document.querySelectorAll(`[id="${fieldId}"]`).forEach(field => {
                const input = field.querySelector("input") || field.querySelector("span") || field;
                const value = input.textContent || input.value || "";
                setFieldColor(field, value, 'yarn');
            });
        });
    };

    // WeFormSDK事件绑定初始化
    const initWeFormSDKBindings = () => {
        if (!window.WeFormSDK || window._boundTables) return;
        
        window._boundTables = true;
        const weFormSdk = window.WeFormSDK.getWeFormInstance();

        // 绑定明细表字段
        CONFIG.detailTables.forEach(tableName => {
            const tableId = weFormSdk.convertFieldNameToId(tableName);
            CONFIG.detailFields.forEach(fieldName => {
                const fieldMark = weFormSdk.convertFieldNameToId(fieldName, tableId);
                weFormSdk.bindFieldChangeEvent(fieldMark, (data) => {
                    const fields = document.querySelectorAll('[fieldid="' + data.id.slice(5) + '"]');
                    fields.forEach(field => {
                        setFieldColor(field, data.value, 'standard');
                    });
                });
            });
        });

        // 绑定供应商描述字段
        CONFIG.supplierDescFields.forEach((fieldName, index) => {
            const fieldId = weFormSdk.convertFieldNameToId(fieldName, "main");
            weFormSdk.bindFieldChangeEvent(fieldId, (data) => {
                const supplierNum = index + 1;
                fieldValueArr[index] = data.value || "";
                flagHideArr[index] = !!data.value;
                updateSupplierLabel(supplierNum, data.value);
                initAllFieldColors(); // 重新初始化颜色
            });
        });
    };

    // 事件委托 - 处理动态元素
    const setupEventDelegation = () => {
        ['input', 'change'].forEach(eventType => {
            document.addEventListener(eventType, (e) => {
                const field = e.target.closest('[id="yin1"]') || 
                             e.target.closest(CONFIG.yarnFields.map(f => `[id="${f}"]`).join(','));
                
                if (field) {
                    const value = e.target.value || e.target.textContent || "";
                    const fieldType = field.getAttribute('id') === 'yin1' ? 'price' : 'yarn';
                    setFieldColor(field, value, fieldType);
                }
            }, true);
        });
    };

    // MutationObserver - 监听DOM变化，增强版支持表格放大
    const setupMutationObserver = () => {
        const observer = new MutationObserver((mutations) => {
            let shouldUpdateColors = false;
            let shouldUpdateSuppliers = false;
            
            mutations.forEach(mutation => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === 1) {
                            // 检查是否包含需要处理的字段
                            const hasTargetFields = CONFIG.allFields.some(fieldId =>
                                node.querySelector && (
                                    node.querySelector(`[id="${fieldId}"]`) ||
                                    node.id === fieldId
                                )
                            );
                            
                            // 检查是否包含供应商元素
                            const hasSupplierElements = CONFIG.supplierFields.some(fieldId =>
                                node.querySelector && (
                                    node.querySelector(`[id="${fieldId}"]`) ||
                                    node.id === fieldId
                                )
                            );
                            
                            // 检查是否是表格相关的DOM变化
                            const isTableRelated = node.querySelector && (
                                node.querySelector('table') ||
                                node.tagName === 'TABLE' ||
                                node.querySelector('tr') ||
                                node.tagName === 'TR' ||
                                CONFIG.domTables.some(tableId =>
                                    node.querySelector(`#${tableId}`) || node.id === tableId
                                )
                            );
                            
                            if (hasTargetFields || isTableRelated) {
                                shouldUpdateColors = true;
                            }
                            
                            if (hasSupplierElements || isTableRelated) {
                                shouldUpdateSuppliers = true;
                            }
                        }
                    });
                }
            });
            
            if (shouldUpdateColors || shouldUpdateSuppliers) {
                setTimeout(() => {
                    if (shouldUpdateColors) {
                        initAllFieldColors();
                    }
                    if (shouldUpdateSuppliers) {
                        updateAllSupplierLabels();
                    }
                }, 100);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true, // 监听属性变化，如表格放大时的样式变化
            attributeFilter: ['style', 'class'] // 只监听样式和类名变化
        });
    };

    // 初始化供应商描述数据
    const initSupplierDescriptions = () => {
        if (!window.WeFormSDK) return;
        
        const weFormSdk = window.WeFormSDK.getWeFormInstance();
        fieldValueArr = [];
        flagHideArr = [];

        CONFIG.supplierDescFields.forEach((fieldName, index) => {
            const fieldId = weFormSdk.convertFieldNameToId(fieldName, "main");
            const fieldValue = weFormSdk.getFieldValue(fieldId);
            fieldValueArr[index] = fieldValue || "";
            flagHideArr[index] = !!fieldValue;
        });

        setTimeout(() => {
            updateAllSupplierLabels();
            initAllFieldColors();
        }, 1000);
    };

    // 更新所有供应商标签
    const updateAllSupplierLabels = () => {
        CONFIG.supplierDescFields.forEach((_, index) => {
            const supplierNum = index + 1;
            const supplierValue = fieldValueArr[index];
            updateSupplierLabel(supplierNum, supplierValue);
        });
    };

    // 保持原有的工具方法
    window.hideDetail = function(weFormSdk, fieldName, className) {
        const detailMark = weFormSdk.convertFieldNameToId(fieldName);
        const detailRowStr = weFormSdk.getDetailRowCount(detailMark);

        if (detailRowStr == 0) {
            var items = document.getElementsByClassName(className);
            for (var i = 0; i < items.length; i++) {
                items[i].style.display = 'none';
            }
        }
    };

    // 表格放大/缩小监听器
    const setupTableResizeListener = () => {
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            setTimeout(() => {
                updateAllSupplierLabels();
                initAllFieldColors();
            }, 300);
        });

        // 监听表格相关的点击事件（可能触发放大/缩小）
        document.addEventListener('click', (e) => {
            // 检查是否点击了可能影响表格显示的元素
            if (e.target.closest('.table-expand') ||
                e.target.closest('.table-zoom') ||
                e.target.closest('[class*="expand"]') ||
                e.target.closest('[class*="zoom"]') ||
                e.target.closest('[onclick*="table"]') ||
                e.target.closest('[title*="放大"]') ||
                e.target.closest('[title*="缩小"]')) {
                
                setTimeout(() => {
                    updateAllSupplierLabels();
                    initAllFieldColors();
                }, 500);
            }
        });

        // 定期检查供应商标签状态（兜底机制）
        setInterval(() => {
            // 检查是否有供应商标签显示不正确
            let needsUpdate = false;
            CONFIG.supplierDescFields.forEach((_, index) => {
                const supplierNum = index + 1;
                const expectedValue = fieldValueArr[index];
                if (expectedValue) {
                    const elements = document.querySelectorAll(`[id="supplier${supplierNum}"] span`);
                    elements.forEach(span => {
                        if (!span.textContent.includes(expectedValue)) {
                            needsUpdate = true;
                        }
                    });
                }
            });
            
            if (needsUpdate) {
                updateAllSupplierLabels();
            }
        }, 2000); // 每2秒检查一次
    };

    // 主初始化函数
    const init = () => {
        initWeFormSDKBindings();
        initAllFieldColors();
        setupEventDelegation();
        setupMutationObserver();
        setupTableResizeListener(); // 添加表格放大监听
        
        if (window.WeFormSDK) {
            initSupplierDescriptions();
        }
    };

    // 页面就绪事件处理
    if (typeof pageSdk !== 'undefined') {
        pageSdk.on('formReady', (args) => {
            setTimeout(() => {
                initSupplierDescriptions();
                initAllFieldColors();
                console.warn('changeColor completed for all tables');
            }, 3000);
        });
    }

    // 启动初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // 兼容性检查
    if (window.WeFormSDK) {
        console.warn("window.WeFormSDK loaded");
    }

})();