#!/usr/bin/env python3
"""
数据库操作模块
用于管理联通查询系统的SQLite数据库
"""

import sqlite3
import json
import logging
from datetime import datetime
import threading
import os
import hashlib
import time

# 导入统一时区配置模块
from timezone_config import setup_timezone, ShanghaiTimeFormatter, get_current_shanghai_time

# 设置时区
setup_timezone()

# 设置日志
logger = logging.getLogger(__name__)

# 为日志设置上海时区格式化器
formatter = ShanghaiTimeFormatter('%(asctime)s - %(levelname)s - %(message)s')
for handler in logger.handlers:
    handler.setFormatter(formatter)

# 数据库文件路径
DB_FILE = 'query_results.db'

# 数据库连接锁，确保线程安全
db_lock = threading.Lock()

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_file=DB_FILE):
        self.db_file = db_file
        self.init_database()
    
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_file)
        conn.row_factory = sqlite3.Row  # 使查询结果可以像字典一样访问
        return conn
    
    def init_database(self):
        """初始化数据库表"""
        try:
            with db_lock:
                conn = self.get_connection()
                cursor = conn.cursor()
                
                # 创建用户表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS users (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        username TEXT UNIQUE NOT NULL,
                        password_hash TEXT NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        last_login DATETIME
                    )
                ''')

                # 创建系统设置表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_settings (
                        key TEXT PRIMARY KEY,
                        value TEXT,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # 创建查询结果表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS query_results (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        phone TEXT UNIQUE NOT NULL,
                        total REAL DEFAULT 0,
                        available REAL DEFAULT 0,
                        vouchers TEXT DEFAULT '',
                        tasks TEXT DEFAULT '[]',
                        last_query DATETIME DEFAULT CURRENT_TIMESTAMP,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建索引
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_phone ON query_results(phone)
                ''')
                
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_last_query ON query_results(last_query)
                ''')
                
                conn.commit()
                conn.close()
                logger.info("数据库初始化完成")
                
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def save_query_results(self, results):
        """
        保存查询结果到数据库
        
        Args:
            results (dict): 查询结果字典，格式为 {phone: {total, available, vouchers, tasks, last_query}}
        
        Returns:
            bool: 保存是否成功
        """
        try:
            with db_lock:
                conn = self.get_connection()
                cursor = conn.cursor()
                
                for phone, data in results.items():
                    # 处理vouchers和tasks数据
                    vouchers_str = data.get('vouchers', '')
                    if isinstance(vouchers_str, (list, dict)):
                        vouchers_str = json.dumps(vouchers_str, ensure_ascii=False)
                    
                    tasks_str = data.get('tasks', [])
                    if isinstance(tasks_str, list):
                        tasks_str = json.dumps(tasks_str, ensure_ascii=False)
                    
                    # 使用REPLACE INTO来插入或更新数据
                    cursor.execute('''
                        INSERT OR REPLACE INTO query_results 
                        (phone, total, available, vouchers, tasks, last_query, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        phone,
                        float(data.get('total', 0)),
                        float(data.get('available', 0)),
                        vouchers_str,
                        tasks_str,
                        data.get('last_query', get_current_shanghai_time()),
                        get_current_shanghai_time()
                    ))
                
                conn.commit()
                conn.close()
                logger.info(f"成功保存 {len(results)} 条查询结果到数据库")
                return True
                
        except Exception as e:
            logger.error(f"保存查询结果失败: {e}")
            return False
    
    def get_all_results(self):
        """
        获取所有查询结果
        
        Returns:
            dict: 查询结果字典
        """
        try:
            with db_lock:
                conn = self.get_connection()
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT phone, total, available, vouchers, tasks, last_query
                    FROM query_results
                    ORDER BY updated_at DESC
                ''')
                
                rows = cursor.fetchall()
                conn.close()
                
                results = {}
                for row in rows:
                    phone = row['phone']
                    
                    # 解析JSON字段
                    try:
                        vouchers = json.loads(row['vouchers']) if row['vouchers'] else ''
                    except:
                        vouchers = row['vouchers']
                    
                    try:
                        tasks = json.loads(row['tasks']) if row['tasks'] else []
                    except:
                        tasks = []
                    
                    results[phone] = {
                        'total': row['total'],
                        'available': row['available'],
                        'vouchers': vouchers,
                        'tasks': tasks,
                        'last_query': row['last_query']
                    }
                
                logger.info(f"从数据库获取 {len(results)} 条查询结果")
                return results
                
        except Exception as e:
            logger.error(f"获取查询结果失败: {e}")
            return {}
    
    def get_result_by_phone(self, phone):
        """
        根据手机号获取查询结果
        
        Args:
            phone (str): 手机号
            
        Returns:
            dict: 查询结果或None
        """
        try:
            with db_lock:
                conn = self.get_connection()
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT phone, total, available, vouchers, tasks, last_query
                    FROM query_results
                    WHERE phone = ?
                ''', (phone,))
                
                row = cursor.fetchone()
                conn.close()
                
                if row:
                    # 解析JSON字段
                    try:
                        vouchers = json.loads(row['vouchers']) if row['vouchers'] else ''
                    except:
                        vouchers = row['vouchers']
                    
                    try:
                        tasks = json.loads(row['tasks']) if row['tasks'] else []
                    except:
                        tasks = []
                    
                    return {
                        'total': row['total'],
                        'available': row['available'],
                        'vouchers': vouchers,
                        'tasks': tasks,
                        'last_query': row['last_query']
                    }
                
                return None
                
        except Exception as e:
            logger.error(f"获取手机号 {phone} 的查询结果失败: {e}")
            return None
    
    def clear_all_results(self):
        """清空所有查询结果"""
        try:
            with db_lock:
                conn = self.get_connection()
                cursor = conn.cursor()

                cursor.execute('DELETE FROM query_results')
                conn.commit()
                conn.close()

                logger.info("已清空所有查询结果")
                return True

        except Exception as e:
            logger.error(f"清空查询结果失败: {e}")
            return False

    def clear_all_users(self):
        """清空所有用户数据"""
        try:
            with db_lock:
                conn = self.get_connection()
                cursor = conn.cursor()

                # 删除所有用户
                cursor.execute('DELETE FROM users')

                # 删除注册标记
                cursor.execute('DELETE FROM system_settings WHERE key = ?', ('has_registered_user',))

                conn.commit()
                conn.close()

                logger.info("已清空所有用户数据")
                return True

        except Exception as e:
            logger.error(f"清空用户数据失败: {e}")
            return False
    
    def get_statistics(self):
        """获取统计信息"""
        try:
            with db_lock:
                conn = self.get_connection()
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT 
                        COUNT(*) as total_accounts,
                        SUM(total) as total_points,
                        SUM(available) as total_available,
                        MAX(last_query) as latest_query
                    FROM query_results
                ''')
                
                row = cursor.fetchone()
                conn.close()
                
                if row:
                    return {
                        'total_accounts': row['total_accounts'],
                        'total_points': row['total_points'] or 0,
                        'total_available': row['total_available'] or 0,
                        'latest_query': row['latest_query']
                    }
                
                return {
                    'total_accounts': 0,
                    'total_points': 0,
                    'total_available': 0,
                    'latest_query': None
                }

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {
                'total_accounts': 0,
                'total_points': 0,
                'total_available': 0,
                'latest_query': None
            }

    def hash_password(self, password):
        """对密码进行哈希处理"""
        return hashlib.sha256(password.encode('utf-8')).hexdigest()

    def verify_password(self, password, password_hash):
        """验证密码"""
        return self.hash_password(password) == password_hash

    def has_registered_user(self):
        """检查是否已有注册用户"""
        try:
            with db_lock:
                conn = self.get_connection()
                cursor = conn.cursor()

                cursor.execute('SELECT COUNT(*) FROM users')
                count = cursor.fetchone()[0]
                conn.close()

                return count > 0

        except Exception as e:
            logger.error(f"检查注册用户失败: {e}")
            return False

    def register_user(self, username, password):
        """
        注册新用户

        Args:
            username (str): 用户名
            password (str): 密码

        Returns:
            dict: 注册结果
        """
        try:
            # 检查是否已有用户注册
            if self.has_registered_user():
                return {
                    'success': False,
                    'message': '系统已有用户注册，不允许重复注册'
                }

            # 验证用户名和密码
            if not username or len(username.strip()) < 3:
                return {
                    'success': False,
                    'message': '用户名至少需要3个字符'
                }

            if not password or len(password) < 6:
                return {
                    'success': False,
                    'message': '密码至少需要6个字符'
                }

            with db_lock:
                conn = self.get_connection()
                cursor = conn.cursor()

                # 再次检查是否已有用户（防止并发注册）
                cursor.execute('SELECT COUNT(*) FROM users')
                if cursor.fetchone()[0] > 0:
                    conn.close()
                    return {
                        'success': False,
                        'message': '系统已有用户注册，不允许重复注册'
                    }

                # 创建用户
                password_hash = self.hash_password(password)
                cursor.execute('''
                    INSERT INTO users (username, password_hash, created_at)
                    VALUES (?, ?, ?)
                ''', (username.strip(), password_hash, get_current_shanghai_time()))

                # 设置系统标记
                cursor.execute('''
                    INSERT OR REPLACE INTO system_settings (key, value, updated_at)
                    VALUES (?, ?, ?)
                ''', ('has_registered_user', 'true', get_current_shanghai_time()))

                conn.commit()
                conn.close()

                logger.info(f"用户 {username} 注册成功")
                return {
                    'success': True,
                    'message': '注册成功'
                }

        except sqlite3.IntegrityError:
            return {
                'success': False,
                'message': '用户名已存在'
            }
        except Exception as e:
            logger.error(f"用户注册失败: {e}")
            return {
                'success': False,
                'message': f'注册失败: {str(e)}'
            }

    def login_user(self, username, password):
        """
        用户登录

        Args:
            username (str): 用户名
            password (str): 密码

        Returns:
            dict: 登录结果
        """
        try:
            with db_lock:
                conn = self.get_connection()
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT id, username, password_hash FROM users
                    WHERE username = ?
                ''', (username.strip(),))

                user = cursor.fetchone()

                if user and self.verify_password(password, user['password_hash']):
                    # 更新最后登录时间
                    cursor.execute('''
                        UPDATE users SET last_login = ? WHERE id = ?
                    ''', (get_current_shanghai_time(), user['id']))

                    conn.commit()
                    conn.close()

                    logger.info(f"用户 {username} 登录成功")
                    return {
                        'success': True,
                        'message': '登录成功',
                        'user': {
                            'id': user['id'],
                            'username': user['username']
                        }
                    }
                else:
                    conn.close()
                    return {
                        'success': False,
                        'message': '用户名或密码错误'
                    }

        except Exception as e:
            logger.error(f"用户登录失败: {e}")
            return {
                'success': False,
                'message': f'登录失败: {str(e)}'
            }

# 全局数据库管理器实例
db_manager = DatabaseManager()

# 便捷函数
def save_query_results(results):
    """保存查询结果"""
    return db_manager.save_query_results(results)

def get_all_results():
    """获取所有查询结果"""
    return db_manager.get_all_results()

def get_result_by_phone(phone):
    """根据手机号获取查询结果"""
    return db_manager.get_result_by_phone(phone)

def clear_all_results():
    """清空所有查询结果"""
    return db_manager.clear_all_results()

def clear_all_users():
    """清空所有用户数据"""
    return db_manager.clear_all_users()

def get_statistics():
    """获取统计信息"""
    return db_manager.get_statistics()

def has_registered_user():
    """检查是否已有注册用户"""
    return db_manager.has_registered_user()

def register_user(username, password):
    """用户注册"""
    return db_manager.register_user(username, password)

def login_user(username, password):
    """用户登录"""
    return db_manager.login_user(username, password)
