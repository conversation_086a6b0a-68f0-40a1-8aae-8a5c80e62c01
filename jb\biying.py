import requests
import random
import re
import time
import json
import os
from datetime import datetime, date
from urllib.parse import urlparse, parse_qs
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 尝试导入notify，失败则使用本地打印替代
try:
    import notify
except ImportError:
    class Notify:
        def send(self, title, content):
            print("\n--- [通知] ---")
            print(f"标题: {title}")
            print(f"内容:\n{content}")
            print("----------------")
    notify = Notify()

def print_log(title: str, msg: str):
    """打印带时间戳的日志"""
    now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"{now} [{title}]: {msg or ''}")

# 从环境变量获取cookie，支持多行（一行一个）
def get_cookies():
    """从环境变量或手动指定获取cookie，支持多行（一行一个）"""
    # 手动指定的cookie列表（可以直接在这里填入）
    manual_cookies = [
        # 在这里添加你的cookie，每行一个，格式示例：
        "MUID=232E6763C25C6AD411807163C3166B6D; SRCHUSR=DOB=20250603&DS=1; MUIDB=232E6763C25C6AD411807163C3166B6D; tifacfaatcs=chunks-2; tifacfaatcsC1=CfDJ8OmjzpJHIp5HoToS1x8R-_pp_uDr6RH-vQ4dbGWzmdXK4QGE7VwKKmzC1zQiYslIq8siFfD7L5G-Ff9GnR9CvtSfwxxZDx0ElujD2gR9fB8lt5pKQvV4_A6dZjgffYihlRGN2mEuQ8BjSRVdTwdZ1E8W-ULo-7sejhtC4vKPVoVlqu98SNyyx3WYhphjURIELNMDY8OXkvWu7DZekKZcx5hTjpqF3KupqCP2Ao7S7WVYoI7jE1v9AUBXHfJui28ppMSGrCePUChdG1AtJ32wsQweCPSfuz7qpzYpb0hajtDucXVZFdr5koe_kJrihJB75kGBEbwF16220damokQwNXOkJVPVSViGOZk57CChvDCWy2ZVZwQIsKeiPxcQaQo--KHCNOaOQ45Vn7e6vKNydjbv3SlEBMkH7-tSs74cCEdyUmk1Pe5RD9kP3Z7-KoOJAKzYEEuiCQR3MKXtKxGqlMTs13HtAoEbRoAhgPSPrWtU6Q6QcNnx7b1qJPc7kcY_mqeds2bWcYET5j0ha9WIFf55leDfSJsXPoVvP_FdGOzuWiK9gOkmmaXIXouFuNSM9N-5_XbInAxDqJ0OJxfxBDBQAd-LUkw8LwyByY1KXOcU3ln4PfNF_IaQCwke4OkHbOeYi6JgfX05qymghSPqGE9gJBmSiFbmuy9Iwx0SCA4hSknQPB0tKGI5I6xvBrjf8C6ICvlM1bRFayYAnSmyJ6CSXIkKRpW-NRIuGw7zG2P7-sKOt-TktCw4DGs6h7KkZzmISVq08ai8S6lNEwybZbRF93u7d-cKWw_dU719Xi_qHjPYYV1s9uHe1PuNEDVTw7X7L7GUdUVuYJ4LXhappX-tOxHhNDO-GPcSQs-cYibtVY-VCSskmKN_DkvDn6Z6pJ76AFJX4ckZZsJF5UAvldLY-9baYceH-DcIFzjAO5wrV9-uBEr9TF1aJL6k9DMHI1ThqhCtoQ02wubsLNCWBArkd5OCqx35RxNU2W2aNAX1LhiPsPpJIOvFt7nXD4hsDaQIltuvTEnTxjHIirbeg8NmADndpubN49MbPxXMnmUwL5elRTg_auHIE5-iZmuQjkQouJ4H3vuoQtx_y8Y0DgFr7cJh1f9gPksFbxa1KArH7tdyUH5ANRIGVLfDON6C_egKw7QZ6r9t56GVu6NiEAkwOPjufzMK0QOo_FoyFl2RcIfl4sZNQ59bZxgqaBJv5Gtx1pP6ch6MGNC4Im5E0IjJiVQpPKDYM6QjDLes0FxhmQjJejkQtYNabkpH4yI_J1zURGvEzBpGcrVxcGCDETtEKmge-n9iXO-sKOeyfDjs41jWNTpQkFJjBnnwQ_NEdBsKILFeb9U3dn84MdnMK0GilM7-utMKCeIPiLowPqc03DyTMSu1pzqXKao-Mtb3hwFT2Yr7BNbQt9o4qUL8Y0iJa6F7acHozzKwySu8Utx8FKTCAcwej2mBZWxqm31VMP-DKYVwK2O5h6jL3jSkh9yQtIMR65WUS_dR6x2uspnfkZkPCSVtbvtw5R-3TOpqw2xBcp4R6woAC12dXvk_OpsdvFwSloJnH9fWJEbRYarqBI3ebaGf-Ap2LW1n2S-zNDrQUvo-067ThitdYERKWGQf25eAuDmx2aSxjfUcj5kz6NPc0t0nKCpfHH53_-RWnRJBenV_o41Z1AcnA-kVCnrVYJ_EdUr77tJwdZwy2SGff3ama9gTGEOj-DTz7FsHqyI7QbXI1rvPRM3hRXGBrkWLp_CejeWq8IVQZahwDYwmjGt1K0TQlKKXBdzK12SEuC6jdTBK-NbSBjknXerUop1DxnpCNJXBan5FOYa_xJK1bViRqIJZIE3Lx0ijR2MHhoBpNKnJXF4VCLLBfL7m4dS-Ucf-WT3saWQNvHK1m0qupajXvtcyGq1fH0fUeT1vY2R-uETCaDdDWWcMNK6FzMtKuxRRcC6MY11N0YQOwpyhWlNyw7US4y8TgwTNwhoVk2l70Pm3J6n2zZeuJlhxmvCgCqUnxhtOB9LzVh0-TmTi7DWV42l4RV8yrU9VFCNJtAaYUKzOLXCsiCl9UDWfW2z1Q2yBaYF6l9RTSzCxqoo8Ecas0P7_Gn3SpDZ22d_9GoWWPJ-EmCn9jbs5UQZoovd57sL3Q0KE8-E_XUvg310EI6GQN0nbD98meYhE_BqwFc8CNCNaJxM8hz9zH9c60K1EyjjCrCB0EBQXqxz7UeTdCSeY7xfsm62Nlbj16ciL6gNaKFctc-3T-YamxK0Z96zIoRbl8paiJGg9olFwfq4D_IO1jwt-I2DrPEAhMJXWxCrxbEycuemIiiu9Ds4Ts-d-B4QFZgEp5ACWV9hPlUSneF1LoWIJltU7-GYnyZv13AQq_03kDeeT-BTLBvQd6OS47YbDHxb86B_lDrj1DeldI6TS3eRM0-4SLjtAho__coptUqI1LxfTTSd06ydd1HZq8ZMti6CsqG7xZ8XsrnraY5RI0rrsmUnEddtbkYja4oHeeYPj8JQLWVgLmDbhcv6toIAvBE-JMqH5wX4FEP4A19vPxEbwgprIA2JBGGv_Ff1US9fZwZMLpZrUMSZSZRIAUikENRBMfis74TYI8fmLL4AzYm34wd-fBIkdLaZcA884P04Tih46nnhxeZ1fAdcGfaoWm9qJX1vqSSoKUUe8-0RA4eiiVR02ybbtc7WyPiQNugRBCYW_gCvgWnaC5FUTfzlTHPGCzgOQ8aypHXdeTSmmYXymq_RL1_O60y93P5gzWSo7trcWlhqljItNiH55aWjvPBVkA6m3eDZxmlHlK9SqRP5iUttHcmAKgL7kNwK-XiigiW3suu1flYjwx2bdIgQjUf7LhMYnk_fqO2fdrjHdT-J7g7_OnJdFHLYCWkzU_qvkw1AI_tpr2zNHI2LI37OV5L_cZARLwL-zO0Hmxe6zg7oMj-MMQRyPflf0GX_3CN19AVjWMB80OV90evK1HL4RTKe_J2-wwpJpvyYFODXgf20QaIQ0JZcNG3Qp9kNmI9IDwyu--LnjlmTPNL0t852bSjqS2Pa0hTvjrXkD5erEvZCjsUzlnmx2CqnR_vfG5O1NdwH8qKeovso19VXcUcFe0O3V2A-N6H_p19pgv7qJJmkCZBvt4yUROkJnbyOsJlOm7MSdCqK9jpD4oVos0TPEBQgpqnqwrvnDdB_zwzteyTnQjtofcQT5l4yMusVSn4_-FA11AGs0-wka7X4XiEHEEn6mOH4xMUpFONoEr1FICIsCVPNqn52rMT5dx596A2IeIOWKCpeXAp6qrK55IGnp-eYKshF7ae2lLu_AojfBU_SbvXBAq6HBuFFzPCYNC9GqDKXC-z-6Zg0Z67dkP-ANIzCrOG-VfughcISQDXe6gNmPJHxI1FAHiAuE4sQ9P427wxmatUd7g4rvupBgP83GtUAgwnnrfUNHJH_l32VgEE8DUwtjrnAjClwDQpYTJmMZQXEDQya3O0c1ofHcBN1-TPjvSptWgwv2zIoPXpo12140ZN0uav79YLYwz8h9w_2QHmTwetP4z1-koErSZZxL-X3hKqm5jW8bwA0Om_ihoSnzJPDMzXpq1YxtTdNubUGwjRxvbp5cyzJvh8qN8DPZcb1bauwYuyhChXgbz8bVmsm_lj_Sn-1xPBr5BbOyN10n6I91UXgcnyW2MQjDJXa0H6v6npVTaTYiniMPuXJNI6jr_o7ucQsAdTMhSzaRI3_2_1GyCzOahpPQKkzEZrM_7R00R4ctstop8OBgwNuEA2nnX33BfooZCpHdsxJxhqtxMwBSNcewxThD8Gt34v3QL0UhUKdqtAWVoZpzwKPixU-hWAcvUwQiv9BoUiVBtmMsUMYBS3PtIbV95DUb5a0bwWd3kT-1qa72O2kJxxt-e7WSZEnyPIy6jebGEf5XsKN5gw3Q_KXN-xxPzjqEa2USgaOeVpzWZExmmu0npjD; tifacfaatcsC2=TEw6iwg8y9EBAnR5gCHzFnBVcGSaJCEwv2cjcV1OB0Yj8WBSQ5zLZy2OWbCuTsvz9RKoin9IdLHNLOSb0xWSbpPf-pVdB0X_-KFZlmNz0vDaCNPVIncUL10DJ3PJ4; vdp=%7B%22ex%22%3Atrue%2C%22red%22%3Afalse%7D; MicrosoftApplicationsTelemetryDeviceId=469e1408-af54-469d-a6f8-3420a0553d19; MicrosoftApplicationsTelemetryFirstLaunchTime=2025-07-03T08:54:37.634Z; MSCC=cid=nxtlfdpxdt0oqm8qvd2dcr9b-c1=2-c2=2-c3=2; _clck=1q6qgwo%7C2%7Cfxa%7C1%7C2010; USRLOC=HS=1&ELOC=LAT=39.981876373291016|LON=116.35118865966797|N=%E6%B5%B7%E6%B7%80%E5%8C%BA%EF%BC%8C%E5%8C%97%E4%BA%AC%E5%B8%82|ELT=2|&CLOC=LAT=39.97589059708145|LON=116.34089861676352|A=733.4464586120832|TS=250708063436|SRC=W&BID=MjUwNzA4MTQzNDMxXzgyZTMzZmY2ZjM4NjMzMmIzNzgxMTQ3ZjA4MTEzMmU1M2E2MjY4NWFiYWU3MjlmNGRhYjQzMzE2NTIyNzMyYzU=; SRCHHPGUSR=SRCHLANG=zh-Hans&PV=14.0.0&BZA=0&DM=0&BRW=XW&BRH=M&CW=1528&CH=706&SCW=1513&SCH=2819&DPR=1.3&UTC=480&EXLTT=5&HV=1751863273&HVE=CfDJ8Inh5QCoSQBNls38F2rbEpRAhlPC-DXq3p8dsiD_5cZrmcBzxN6F9UvK5csCNJs1d6honD6ADN696Yyduf-mSsE5FVfLmus8tph-LJKIvS_G9V8pgcmX8koPI5x4gwzGQETJ2DJQOng4aHN2b9zhKElx2kYHg4R5G1IKTyuZJstocuumF9m2PMHVA0uwhCRtzw&PRVCW=1528&PRVCH=706&AV=14&ADV=14&RB=0&MB=0; _RwBf=r=1&mta=0&rc=12948&rb=12948&rg=17925&pc=12840&mtu=0&rbb=0.0&clo=0&v=1&l=2025-07-06T07:00:00.0000000Z&lft=0001-01-01T00:00:00.0000000&aof=0&ard=0001-01-01T00:00:00.0000000&rwdbt=1751507679&rwflt=1751507663&rwaul2=0&g=&o=0&p=bingcopilotwaitlist&c=MY00IA&t=976&s=2023-02-18T12:48:48.7572459+00:00&ts=2025-07-07T04:41:13.3509773+00:00&rwred=0&wls=2&wlb=0&wle=0&ccp=2&cpt=0&lka=0&lkt=0&aad=0&TH=&cid=0&gb=2025w17_c&e=y__xCCx76FenCG-KqXPPGbwt0AcojYqHfC4WzhqMjDdyPl3LI6NoKjdMqi1aFA3Kd7caP0XzwrTmmzMloQxKd8Ei7bbOB3GFQ2zel-fN2VI&A=7454FEB98CF7EAA821491D5BFFFFFFFF; _EDGE_S=SID=0992B5C5476D6B2926C6A3E646876A2C; .MSA.Auth=CfDJ8Inh5QCoSQBNls38F2rbEpSGOdmvpZx5GHudk311gswle9frwZDcKhnJwodW1b2UMHMXJTQRGH04SgmEm9n3oBY1WnjnynoLnTFDu4rsfw3WdL36YfSuEHjzWxPRhJphOzr6qshHkpg0abjL3sYWjjDwnCcK8I3Pglup3oH5yu3Fid3zce9pEN440ooJQc_zjmVANrL94wqgmZlhcHqs-rYZHIXJuwN5eviaVEX1sCQCxCSHk0vKk_cY6CvSbcrjeGhekM3GmQVDcwtXStvrDk4Y53xcdILGr8WTO_Z0t5ktdqmJQtS4SJkl4u25_LTXs8g89Bu_ZzAvGqgAbRcKFeFI2lvaBGai9Y-x2Q1eUVuGGsVQAEqkLubl8oDDxrzyvQ; WLS=C=00000000-0000-0000-1910-22809d7e8f35&N=%e5%af%92+%e8%8b%a5; _U=1B4r10YEhyKWU5OJc3iyYqBxXeyz2LF-gPJC_4tvNpGkhb3MEK5eN_niLxCz-gDvWsABhk3PtLJPLLqvPUjgPtUQIraam1UCiNgvYUqYM7IVHJhjCnhUdHLwqbOG07Z7qgHSO9EREmgGHtmy3L7iFfV--hppGE5asN_LFc5Ai9Olu_-5FhsM5IJ-CS8qC7M-wLxvawyr1Sxd6thIF5my7HA; _C_Auth=; .AspNetCore.Antiforgery.icPscOZlg04=CfDJ8OmjzpJHIp5HoToS1x8R-_rYlmPBI9R0UF6q2JX5mgkK6MY5DQmiaEZbGdZnNlUclmsMWTGzMD3wHQBaG5F6U34wulL-RP1FH_WyBaWKQW9qO41sNQVJgYK08fYqxptJ7Xozu2THTTH2lV6oyjZQjdo; GRNID=4bfc437e-d120-4ba1-a2f8-a0ffd1a73bdc; _DPC=U683_rwd3p; SNRHOP=I=&TS=; _SS=SID=0992B5C5476D6B2926C6A3E646876A2C&PC=U683; SRCHS=PC=U683; _uetsid=9f93e4e05bc511f0b5ea2d3f96eac337; _uetvid=0cd8e39015d211f0b785835343747c28; webisession=%7B%22impressionId%22%3A%224cfda6f2-32b9-4aa8-8fa3-a93611e3aa70%22%2C%22sessionid%22%3A%22df401106-cd0a-42c7-893e-464e695f8ad6%22%2C%22sessionNumber%22%3A3%7D; GC=Vmv-X_3ujItPLBcAIOVHxDvjIYmTNkNdAXpu693zhxePrwhUyJJJFwa0TSXqiRU2XTYZOL8EVYdG1bzdVxESCA"
        # "cookie2_value_here",
        # "cookie3_value_here",
    ]
    
    # 优先使用手动指定的cookie
    if manual_cookies and any(cookie.strip() for cookie in manual_cookies):
        # 过滤掉空的cookie
        cookies_list = [ck.strip() for ck in manual_cookies if ck.strip()]
        print_log("配置信息", f"使用手动指定的cookie，共 {len(cookies_list)} 个")
        return cookies_list
    
    # 如果没有手动指定，则从环境变量获取
    env_cookies = os.getenv("bing_ck")
    if env_cookies:
        # 分割多行cookie，去除空行和空白字符
        cookies_list = [ck.strip() for ck in env_cookies.strip().split("\n") if ck.strip()]
        print_log("配置信息", f"使用环境变量bing_ck，共 {len(cookies_list)} 个")
        return cookies_list
    else:
        print_log("配置错误", "未配置 bing_ck 环境变量，也未手动指定cookie，无法执行任务")
        return []

# 获取cookie列表
cookies_list = get_cookies()
if not cookies_list:
    print_log("启动错误", "没有可用的cookie，程序退出")
    exit(1)

print_log("初始化", f"检测到 {len(cookies_list)} 个账号，即将开始...")

# 浏览器通用头部（将在运行时根据当前cookie动态设置）
BROWSER_HEADERS = {
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "referer": "https://rewards.bing.com/"
}

def create_session(proxy=None):
    """创建带有重试机制的会话"""
    session = requests.Session()
    
    # 设置代理
    if proxy:
        session.proxies = {
            'http': proxy,
            'https': proxy
        }
    
    # 设置重试策略
    retry_strategy = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["HEAD", "GET", "POST"]
    )
    
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    
    # 设置默认超时
    session.timeout = (10, 30)  # 连接超时10秒，读取超时30秒
    
    return session

# 代理配置（可选）
PROXY_URL = os.getenv("HTTP_PROXY") or os.getenv("HTTPS_PROXY")

def get_rewards_points(cookies):
    """查询当前积分和账号信息"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Linux; Android 15; 24031PN0DC Build/AQ3A.240627.003) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.7151.115 Mobile Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Accept-Encoding': 'gzip, deflate',
        'Cache-Control': 'max-age=0',
        'Upgrade-Insecure-Requests': '1',
        'X-Search-Location': 'lat=19.3516,long=110.1012,re=-1.0000,disp=%20',
        'Sapphire-OSVersion': '9',
        'Sapphire-Configuration': 'Production',
        'Sapphire-APIVersion': '114',
        'Sapphire-Market': 'zh-CN',
        'X-Search-ClientId': '2E2936301F8D6BFD3225203D1E5F6A0D',
        'Sapphire-DeviceType': 'OPPO  Plus',
        'X-Requested-With': 'com.microsoft.bing',
        'Cookie': cookies
    }

    url = 'https://rewards.bing.com/'
    params = {
        'ssp': '1',
        'safesearch': 'moderate',
        'setlang': 'zh-hans',
        'cc': 'CN',
        'ensearch': '0',
        'PC': 'SANSAAND'
    }

    try:
        session = create_session(PROXY_URL)
        response = session.get(url, headers=headers, params=params, timeout=(10, 30))
        response.raise_for_status()
        
        content = response.text
        
        # 提取积分
        points_pattern = r'"availablePoints":(\d+)'
        points_match = re.search(points_pattern, content)
        
        # 提取邮箱账号
        email_pattern = r'email:\s*"([^"]+)"'
        email_match = re.search(email_pattern, content)
        
        available_points = None
        email = None
        
        if points_match:
            available_points = int(points_match.group(1))
            # print_log("积分查询", f"当前积分: {available_points}")
        else:
            print_log("积分查询", "未找到 availablePoints 值")
            
        if email_match:
            email = email_match.group(1)
            # print_log("账号信息", f"账号: {email}")
        else:
            print_log("账号信息", "未找到 email 值")
            
        return {
            'points': available_points,
            'email': email
        }
            
    except requests.exceptions.ConnectionError as e:
        print_log("积分查询", f"网络连接失败: {e}")
        return None
    except requests.exceptions.Timeout as e:
        print_log("积分查询", f"请求超时: {e}")
        return None
    except requests.exceptions.RequestException as e:
        print_log("积分查询", f"请求失败: {e}")
        return None
    except Exception as e:
        print_log("积分查询", f"发生错误: {e}")
        return None

def bing_search_pc(cookies):
    # 随机生成两个汉字
    hanzi_range = list(range(0x4e00, 0x9fa6))
    q = chr(random.choice(hanzi_range)) + chr(random.choice(hanzi_range))

    url = "https://cn.bing.com/search"
    params = {
        "q": q,
        "qs": "FT",
        "form": "TSASDS"
    }

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Referer": "https://rewards.bing.com/",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Cookie": cookies
    }

    try:
        session = create_session()
        response = session.get(url, params=params, headers=headers, timeout=(10, 30))
        if response.status_code == 200:
            return True
        else:
            print_log("电脑搜索", f"请求失败，状态码: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError as e:
        print_log("电脑搜索", f"网络连接失败: {e}")
        return False
    except requests.exceptions.Timeout as e:
        print_log("电脑搜索", f"请求超时: {e}")
        return False
    except Exception as e:
        print_log("电脑搜索", f"电脑搜索异常: {e}")
        return False

def bing_search_mobile(cookies):
    """执行移动设备搜索（来自bing_search.py）"""
    # 随机生成两个汉字
    q = ''.join(chr(random.randint(0x4e00, 0x9fa5)) for _ in range(2))

    url = "https://cn.bing.com/search"
    params = {
        "q": q,
        "form": "NPII01",
        "filters": "tnTID:\"DSBOS_F29F59C848FA467D96D2F8EEC96FBC7A\" tnVersion:\"8908b7744161474e8812c12c507ece49\" Segment:\"popularnow.carousel\" tnCol:\"39\" tnScenario:\"TrendingTopicsAPI\" tnOrder:\"ef45722b-8213-4953-9c44-57e0dde6ac78\"",
        "ssp": "1",
        "safesearch": "moderate",
        "setlang": "zh-hans",
        "cc": "CN",
        "ensearch": "0",
        "PC": "SANSAAND"
    }

    headers = {
        "host": "cn.bing.com",
        "upgrade-insecure-requests": "1",
        "user-agent": "Mozilla/5.0 (Linux; Android 15; 24031PN0DC Build/AQ3A.240627.003) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.7151.115 Mobile Safari/537.36",
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3",
        "x-search-location": "lat=19.3516,long=110.1012,re=-1.0000,disp=%20",
        "sapphire-osversion": "9",
        "sapphire-configuration": "Production",
        "sapphire-apiversion": "114",
        "sapphire-market": "zh-CN",
        "x-search-clientid": "2E2936301F8D6BFD3225203D1E5F6A0D",
        "sapphire-devicetype": "OPPO  Plus",
        "accept-encoding": "gzip, deflate",
        "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        "cookie": cookies,
        "x-requested-with": "com.microsoft.bing"
    }

    try:
        session = create_session(PROXY_URL)
        response = session.get(url, headers=headers, params=params, timeout=(10, 30))
        if response.status_code == 200:
            return True
        else:
            print_log("移动搜索", f"请求失败，状态码: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError as e:
        print_log("移动搜索", f"网络连接失败: {e}")
        return False
    except requests.exceptions.Timeout as e:
        print_log("移动搜索", f"请求超时: {e}")
        return False
    except Exception as e:
        print_log("移动搜索", f"移动设备搜索异常: {e}")
        return False

def check_points_increase(initial_points, current_points):
    """检查积分是否增加"""
    if initial_points is None or current_points is None:
        return False
    return current_points > initial_points

def get_current_timestamp():
    """获取当前时间戳（13位，毫秒）"""
    return int(time.time() * 1000)

def get_dashboard_data(cookies):
    """统一获取dashboard数据和token"""
    try:
        headers = {
            **BROWSER_HEADERS,
            "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
            "cookie": cookies
        }
        session = create_session(PROXY_URL)
        resp = session.get("https://rewards.bing.com/", headers=headers, timeout=(10, 30))
        resp.raise_for_status()
        
        html_text = resp.text
        token_match = re.search(r'name="__RequestVerificationToken".*?value="([^"]+)"', html_text)
        dashboard_match = re.search(r'var dashboard\s*=\s*(\{.*?\});', html_text, re.DOTALL)
        
        if not token_match:
            print_log('Dashboard错误', "未能获取 __RequestVerificationToken")
            return None
        
        if not dashboard_match:
            print_log('Dashboard错误', "未能获取 dashboard 数据")
            return None
        
        token = token_match.group(1)
        dashboard_json = json.loads(dashboard_match.group(1).rstrip().rstrip(';'))
        
        return {
            'dashboard_data': dashboard_json,
            'token': token
        }
    except Exception as e:
        print_log('Dashboard错误', str(e))
        return None

def complete_daily_set_tasks(cookies):
    """完成每日活动任务"""
    # print_log('每日活动', '--- 开始检查网页端每日活动 ---')
    completed_count = 0
    try:
        # 获取dashboard数据
        dashboard_result = get_dashboard_data(cookies)
        if not dashboard_result:
            return completed_count
        
        dashboard_data = dashboard_result['dashboard_data']
        token = dashboard_result['token']
        
        # 提取积分信息
        if 'userStatus' in dashboard_data:
            user_status = dashboard_data['userStatus']
            available_points = user_status.get('availablePoints', 0)
            lifetime_points = user_status.get('lifetimePoints', 0)
            print_log("每日活动", f"✅ 当前积分: {available_points}, 总积分: {lifetime_points}")
        
        # 提取每日任务
        today_str = date.today().strftime('%m/%d/%Y')
        daily_tasks = dashboard_data.get('dailySetPromotions', {}).get(today_str, [])
        
        if not daily_tasks:
            print_log("每日活动", "没有找到今日的每日活动任务")
            return completed_count
        
        # 过滤未完成的任务
        incomplete_tasks = [task for task in daily_tasks if not task.get('complete')]
        
        if not incomplete_tasks:
            print_log("每日活动", "所有每日活动任务已完成")
            return completed_count
        
        print_log("每日活动", f"找到 {len(incomplete_tasks)} 个未完成的每日活动任务")
        
        # 执行任务
        for i, task in enumerate(incomplete_tasks, 1):
            print_log("每日活动", f"执行任务 {i}/{len(incomplete_tasks)}: {task.get('title', '未知任务')}")
            
            if execute_task(task, token, cookies):
                completed_count += 1
                print_log("每日活动", f"✅ 任务完成: {task.get('title', '未知任务')}")
            else:
                print_log("每日活动", f"❌ 任务失败: {task.get('title', '未知任务')}")
            
            # 随机延迟
            time.sleep(random.uniform(2, 4))
        
        print_log("每日活动", f"每日活动执行完成，成功完成 {completed_count} 个任务")
        
    except Exception as e:
        print_log('每日活动出错', f"异常: {e}")
    
    return completed_count

def setup_task_headers(cookies):
    """设置任务执行的请求头"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-User': '?1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Ch-Ua': '"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Ch-Ua-Platform-Version': '"19.0.0"',
        'Sec-Ch-Ua-Model': '""',
        'Sec-Ch-Ua-Bitness': '"64"',
        'Sec-Ch-Prefers-Color-Scheme': 'light',
        'Sec-Ms-Gec': '1',
        'Sec-Ms-Gec-Version': '1-137.0.3296.83',
        'Cookie': cookies
    }
    return headers

def setup_api_headers(cookies):
    """设置API请求的请求头"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'X-Requested-With': 'XMLHttpRequest',
        'Origin': 'https://rewards.bing.com',
        'Referer': 'https://rewards.bing.com/',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Ch-Ua': '"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Ch-Ua-Platform-Version': '"19.0.0"',
        'Sec-Ch-Ua-Model': '""',
        'Sec-Ch-Ua-Bitness': '"64"',
        'Sec-Ch-Prefers-Color-Scheme': 'light',
        'Sec-Ms-Gec': '1',
        'Sec-Ms-Gec-Version': '1-137.0.3296.83',
        'Cookie': cookies
    }
    return headers

def extract_tasks(more_promotions):
    """提取任务"""
    tasks = []
    for promotion in more_promotions:
        priority = promotion.get('priority')
        complete = promotion.get('complete')
        promotion_type = promotion.get('promotionType')
        # 检查是否被锁定
        locked_status = promotion.get('exclusiveLockedFeatureStatus')
        if priority == 0 and complete == False and locked_status != 'locked':
            tasks.append(promotion)
    if not tasks:
        for promotion in more_promotions:
            priority = promotion.get('priority')
            complete = promotion.get('complete')
            promotion_type = promotion.get('promotionType')
            locked_status = promotion.get('exclusiveLockedFeatureStatus')
            if (priority == 1 and complete == False and promotion_type == 'urlreward' and locked_status != 'locked'):
                tasks.append(promotion)
    
    # 继续查找priority=7的任务，不管前面是否找到了其他优先级的任务
    for promotion in more_promotions:
        priority = promotion.get('priority')
        complete = promotion.get('complete')
        promotion_type = promotion.get('promotionType')
        locked_status = promotion.get('exclusiveLockedFeatureStatus')
        if (priority == 7 and complete == False and promotion_type == 'urlreward' and locked_status != 'locked'):
            tasks.append(promotion)
    
    return tasks

def extract_search_query(destination_url):
    """从URL中提取搜索查询"""
    try:
        parsed_url = urlparse(destination_url)
        query_params = parse_qs(parsed_url.query)
        if 'q' in query_params:
            search_query = query_params['q'][0]
            import urllib.parse
            search_query = urllib.parse.unquote(search_query)
            return search_query
        return None
    except Exception as e:
        print_log("更多活动", f"提取搜索查询失败: {e}")
        return None

def report_activity(task, token, cookies):
    """报告任务活动，真正完成任务"""
    if not token:
        print_log("更多活动", "❌ 缺少RequestVerificationToken，无法报告活动")
        return False
    
    try:
        post_url = 'https://rewards.bing.com/api/reportactivity?X-Requested-With=XMLHttpRequest'
        post_headers = setup_api_headers(cookies)
        
        payload = f"id={task['name']}&hash={task.get('hash', '')}&timeZone=480&activityAmount=1&dbs=0&form=&type=&__RequestVerificationToken={token}"
        
        session = create_session(PROXY_URL)
        response = session.post(post_url, data=payload, headers=post_headers, timeout=(10, 30))
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get("activity") and result["activity"].get("points", 0) > 0:
                    print_log("更多活动", f"✅ 获得{result['activity']['points']}积分")
                    return True
                else:
                    print_log("更多活动", f"⚠️ 未获得积分")
                    return False
            except json.JSONDecodeError:
                print_log("更多活动", f"⚠️ 活动报告返回内容无法解析")
                return False
        else:
            print_log("更多活动", f"❌ 活动报告请求失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print_log("更多活动", f"❌ 报告活动时出错: {e}")
        return False

def execute_task(task, token, cookies):
    """执行单个任务"""
    try:
        destination_url = task.get('destinationUrl') or task.get('attributes', {}).get('destination')
        if not destination_url:
            print_log("更多活动", f"❌ 任务 {task.get('name')} 没有目标URL")
            return False
        
        # 检查是否为搜索任务
        search_query = extract_search_query(destination_url)
        
        if search_query:
            # 搜索任务
            print_log("更多活动", f"🔍 执行搜索任务: {task.get('title')}")
        else:
            # 非搜索任务（如Edge相关任务）
            print_log("更多活动", f"🌐 执行URL访问任务: {task.get('title')}")
            
            # 对于Edge相关任务，可能需要特殊处理URL
            if 'microsoftedgewelcome.microsoft.com' in destination_url:
                # 转换为实际的Microsoft URL
                if 'focus=privacy' in destination_url:
                    destination_url = 'https://www.microsoft.com/zh-cn/edge/welcome?exp=e155&form=ML23ZX&focus=privacy&cs=2175697442'
                elif 'focus=performance' in destination_url:
                    destination_url = 'https://www.microsoft.com/zh-cn/edge/welcome?exp=e155&form=ML23ZX&focus=performance&cs=2175697442'
        
        # 设置任务执行请求头
        headers = setup_task_headers(cookies)
        
        # 发送请求
        session = create_session(PROXY_URL)
        response = session.get(
            destination_url, 
            headers=headers, 
            timeout=(10, 30),
            allow_redirects=True
        )
        
        if response.status_code == 200:
            print_log("更多活动", f"✅ 任务执行成功")
            # 报告活动
            if report_activity(task, token, cookies):
                return True
            else:
                print_log("更多活动", f"⚠️ 任务执行成功但活动报告失败")
                return False
        else:
            print_log("更多活动", f"❌ 任务执行失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print_log("更多活动", f"❌ 执行任务时出错: {e}")
        return False

def complete_more_activities(cookies):
    """完成更多活动任务"""
    # print_log('更多活动', '--- 开始检查更多活动 ---')
    completed_count = 0
    
    try:
        # 获取dashboard数据
        dashboard_result = get_dashboard_data(cookies)
        if not dashboard_result:
            print_log("更多活动", "无法获取dashboard数据，跳过更多活动\n")
            return completed_count
        
        dashboard_data = dashboard_result['dashboard_data']
        token = dashboard_result['token']
        
        # 提取积分信息
        if 'userStatus' in dashboard_data:
            user_status = dashboard_data['userStatus']
            available_points = user_status.get('availablePoints', 0)
            lifetime_points = user_status.get('lifetimePoints', 0)
            print_log("更多活动", f"✅ 当前积分: {available_points}, 总积分: {lifetime_points}")
        
        # 提取更多活动任务
        more_promotions = dashboard_data.get('morePromotions', [])
        tasks = extract_tasks(more_promotions)
        
        if not tasks:
            print_log("更多活动", "没有找到可执行的更多活动任务\n")
            return completed_count
        
        print_log("更多活动", f"找到 {len(tasks)} 个可执行的更多活动任务")
        
        # 执行任务
        for i, task in enumerate(tasks, 1):
            print_log("更多活动", f"执行任务 {i}/{len(tasks)}: {task.get('title', '未知任务')}")
            
            if execute_task(task, token, cookies):
                completed_count += 1
                print_log("更多活动", f"✅ 任务完成: {task.get('title', '未知任务')}")
            else:
                print_log("更多活动", f"❌ 任务失败: {task.get('title', '未知任务')}")
            
            # 随机延迟
            time.sleep(random.uniform(2, 4))
        
        print_log("更多活动", f"更多活动执行完成，成功完成 {completed_count} 个任务\n")
        
    except Exception as e:
        print_log('更多活动出错', f"异常: {e}\n")
    
    return completed_count

def perform_search_tasks(search_type, search_func, max_count, initial_points, cookies, check_interval=3):
    """执行搜索任务的通用函数"""
    print_log(search_type, f"--- 开始执行{max_count}次{search_type} ---")
    count = 0
    last_check_points = initial_points
    
    for i in range(max_count):
        count += 1
        
        if search_func(cookies):
            delay = random.randint(15, 30)
            print_log(search_type, f"第 {count} 次{search_type}成功，等待 {delay} 秒...")
            time.sleep(delay)
        else:
            print_log(search_type, f"第 {count} 次{search_type}失败")
        
        # 每check_interval次检查积分
        if count % check_interval == 0:
            current_data = get_rewards_points(cookies)
            if current_data and current_data['points']:
                if check_points_increase(last_check_points, current_data['points']):
                    print_log("积分变化", f"--- 检查积分变化，积分已增加: {last_check_points} -> {current_data['points']}")
                    last_check_points = current_data['points']
                else:
                    print_log("积分变化", f"--- 检查积分变化，积分未增加，停止搜索")
                    break
            else:
                print_log("积分查询", "无法获取当前积分")
                break
    if count == 3:
        count = 0
    return count

def single_account_main(cookies, account_index):
    """单个账号的完整任务流程"""
    print(f"\n{'='*15} [开始处理账号 {account_index}] {'='*15}")
    
    # 1. 查询初始积分和账号信息
    print_log("账号信息", "---查询账号信息和初始积分 ---")
    initial_data = get_rewards_points(cookies)
    if initial_data is None or initial_data['points'] is None:
        print_log("账号信息", "无法获取初始积分，跳过此账号")
        return None
    
    script_start_points = initial_data['points']
    email = initial_data.get('email', '未知邮箱')
    print_log("账号信息", f"账号: {email}, 初始积分: {script_start_points}")
    
    # 2. 执行电脑搜索
    pc_count = perform_search_tasks("电脑搜索", bing_search_pc, 30, script_start_points, cookies)
    
    # 获取电脑搜索完成后的积分，作为移动搜索的基准
    pc_completed_points = get_rewards_points(cookies)
    mobile_start_points = pc_completed_points['points'] if pc_completed_points else script_start_points
    
    # 3. 执行移动设备搜索
    mobile_count = perform_search_tasks("移动搜索", bing_search_mobile, 20, mobile_start_points, cookies)
    
    # 4. 执行每日活动任务
    print_log("每日活动", "--- 开始执行每日活动任务 ---")
    daily_tasks_completed = complete_daily_set_tasks(cookies)
    # print_log("每日活动", f"完成每日活动任务，完成任务数: {daily_tasks_completed}")
    
    # 5. 执行更多活动任务
    print_log("更多活动", "--- 开始执行更多活动任务 ---")
    more_activities_completed = complete_more_activities(cookies)
    # print_log("更多活动", f"完成更多活动任务，完成任务数: {more_activities_completed}")
    
    # 6. 最终积分查询
    final_data = get_rewards_points(cookies)
    
    if final_data and final_data['points'] is not None:
        final_points = final_data['points']
        points_earned = final_points - script_start_points
        print_log("脚本完成", f"最终积分: {final_points} (+{points_earned})")
        
        # 生成账号总结
        summary = (
            f"账号: {email}\n"
            f"✨ 积分变化: {script_start_points} -> {final_points} (+{points_earned})\n"
            f"✨ 电脑搜索: {pc_count} 次\n"
            f"✨ 移动搜索: {mobile_count} 次\n"
            f"✨ 每日活动: {daily_tasks_completed} 个\n"
            f"✨ 更多活动: {more_activities_completed} 个"
        )
        
        return summary
    else:
        print_log("脚本完成", "无法获取最终积分")
        return None

def main():
    """主函数 - 支持多账号执行和推送"""
    all_summaries = []
    
    for i, cookies in enumerate(cookies_list, 1):
        try:
            summary = single_account_main(cookies, i)
            if summary:
                all_summaries.append(summary)
            
            # 账号间延迟（除了最后一个账号）
            if i < len(cookies_list):
                wait_time = random.randint(20, 40)
                print_log("账号切换", f"等待 {wait_time}s 后继续...")
                time.sleep(wait_time)
                
        except Exception as e:
            print_log(f"账号{i}错误", f"处理账号时发生异常: {e}")
            continue
    
    # --- 统一推送 ---
    print(f"\n\n{'='*10} [全部任务完成] {'='*10}")
    if all_summaries:
        print_log("统一推送", "准备发送所有账号的总结报告...")
        try:
            title = f"Microsoft Rewards 任务总结 ({date.today().strftime('%Y-%m-%d')})"
            content = "\n\n".join(all_summaries)
            notify.send(title, content)
            print_log("推送成功", "总结报告已发送。")
        except Exception as e:
            print_log("推送失败", f"发送总结报告时出错: {e}")
    else:
        print_log("统一推送", "没有可供推送的账号信息。")

if __name__ == "__main__":
    main()
