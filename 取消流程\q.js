function parseNumber(value) {
    if (!value) return null;
    const matches = String(value).match(/[+-]?([0-9,]*\.?[0-9]+)/);
    return matches ? Number(matches[0].replace(/,/g, '')) : null;
}

function setFieldColor(field) {
    const input = field.querySelector("input, span") || field;
    const numValue = parseNumber(input.textContent || input.value);
    field.style.backgroundColor = (numValue > 0) ? "red" : "";
}

// zjfd字段监听
if (window.WeFormSDK) {
    const weFormSdk = window.WeFormSDK.getWeFormInstance();
    ['mxb1', 'mxb2'].forEach(table => {
        const fieldMark = weFormSdk.convertFieldNameToId('zjfd', weFormSdk.convertFieldNameToId(table));
        weFormSdk.bindFieldChangeEvent(fieldMark, (data) => {
            document.querySelectorAll(`[fieldid^="${data.id.slice(5)}"]`).forEach(setFieldColor);
        });
    });
}

// DOM监听和页面初始化
new MutationObserver(mutations => {
    mutations.forEach(mutation => {
        mutation.addedNodes.forEach(node => {
            if (node.nodeType === 1) {
                ['zjfd', 'yin1'].forEach(id => {
                    const field = node.querySelector(`[id="${id}"]`) || (node.id === id ? node : null);
                    if (field) setFieldColor(field);
                });
            }
        });
    });
}).observe(document.getElementById('table11') || document.body, { childList: true, subtree: true });

pageSdk.on('formReady', () => {
    setTimeout(() => ['zjfd', 'yin1'].forEach(id =>
        document.querySelectorAll(`[id="${id}"]`).forEach(setFieldColor)
    ), 3000);
});
