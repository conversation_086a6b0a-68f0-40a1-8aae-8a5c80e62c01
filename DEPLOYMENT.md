# 联通查询系统部署指南

## 🚀 快速部署

### 一键部署（推荐）

```bash
chmod +x deploy.sh
./deploy.sh
```

## 📋 系统要求

- **Docker方式**：Docker 20.10+
- **直接部署**：Python 3.8+ 和 pip

## 🔧 部署方式

### 方式一：Docker部署（推荐）

1. **确保Docker已安装**
   ```bash
   docker --version
   ```

2. **运行部署脚本**
   ```bash
   ./deploy.sh
   ```

3. **访问系统**
   - 地址：http://localhost:2032
   - 首次访问需要注册账号

### 方式二：直接部署

1. **安装Python依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **启动服务**
   ```bash
   python server.py
   ```

## 🔑 首次使用

1. **注册账号**
   - 访问 http://localhost:2032
   - 点击"注册"选项卡
   - 输入用户名（≥3字符）和密码（≥6字符）
   - 系统只允许注册一次，注册后注册按钮会隐藏

2. **登录系统**
   - 使用注册的账号登录
   - 登录有效期为365天，除非手动退出

3. **开始查询**
   - 登录后可直接进行查询，无需输入查询密码
   - 支持手动查询和自动定时查询

## ✨ 主要功能

### 🔐 用户管理
- **注册登录系统**：基于SQLite数据库的用户管理
- **一次性注册**：系统只允许注册一个用户，确保安全性
- **长期登录**：登录有效期365天，减少重复登录
- **密码加密**：使用SHA256哈希加密存储密码

### 📊 查询功能
- **无密码查询**：移除了查询密码验证，简化操作
- **实时进度**：查询过程中显示详细进度信息
- **自动查询**：支持30分钟到12小时的定时查询
- **手动查询**：随时可以手动触发查询

### 🗄️ 数据管理
- **SQLite存储**：查询结果持久化存储在本地数据库
- **数据一致性**：页面数据从数据库读取，确保数据一致性
- **历史记录**：保存所有查询历史，支持数据追溯

### 🎨 用户界面
- **现代化设计**：美观的卡片式布局
- **响应式设计**：完美适配桌面和移动设备
- **主题切换**：支持深色/浅色主题切换
- **实时统计**：动态显示账户统计信息

## 📁 文件结构

```
.
├── server.py          # 主服务器文件
├── database.py        # 数据库操作模块
├── ltcx.py           # 联通查询核心模块
├── index.html        # 前端页面
├── Dockerfile        # Docker构建文件
├── deploy.sh         # 一键部署脚本
├── requirements.txt  # Python依赖
├── query_results.db  # SQLite数据库（运行时生成）
└── DEPLOYMENT.md     # 部署说明文档
```

## 🔧 配置说明

### 环境变量
- `TZ=Asia/Shanghai`：设置时区为上海时间
- `FIRST_DEPLOY=true`：首次部署标志

### 端口配置
- 默认端口：2032
- 可在server.py中修改端口设置

### 数据库配置
- 数据库文件：query_results.db
- 自动创建表结构
- 支持用户数据和查询结果存储

## 🛠️ 常用命令

### Docker方式
```bash
# 查看容器状态
docker ps

# 查看日志
docker logs unicom-query

# 停止服务
docker stop unicom-query

# 重启服务
docker restart unicom-query

# 删除容器
docker rm unicom-query

# 重新部署
./deploy.sh
```

### 直接部署方式
```bash
# 查看进程
ps aux | grep server.py

# 查看日志
tail -f server.log

# 停止服务
pkill -f "python.*server.py"

# 重启服务
python server.py
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep 2032
   
   # 杀死占用进程
   sudo kill -9 <PID>
   ```

2. **数据库权限问题**
   ```bash
   # 检查数据库文件权限
   ls -la query_results.db
   
   # 修改权限
   chmod 644 query_results.db
   ```

3. **Python依赖问题**
   ```bash
   # 重新安装依赖
   pip install -r requirements.txt --force-reinstall
   ```

4. **Docker构建失败**
   ```bash
   # 清理Docker缓存
   docker system prune -a
   
   # 重新构建
   docker build -t unicom-query:latest .
   ```

### 日志分析
- Docker方式：`docker logs unicom-query`
- 直接部署：`cat server.log`

## 🔒 安全注意事项

1. **网络安全**
   - 建议在内网环境使用
   - 如需公网访问，请配置防火墙和SSL证书

2. **数据安全**
   - 定期备份数据库文件
   - 密码使用强密码策略

3. **系统安全**
   - 定期更新系统和依赖
   - 监控系统资源使用情况

## 📞 技术支持

如遇到问题，请检查：
1. 系统日志信息
2. 网络连接状态
3. 依赖安装情况
4. 端口占用情况

---

**版本信息**
- 系统版本：v2.0
- 更新日期：2025-06-16
- 主要更新：注册登录系统、SQLite数据库、无密码查询
