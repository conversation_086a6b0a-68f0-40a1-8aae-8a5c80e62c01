<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>自助查询服务</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="shortcut icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="stylesheet" href="https://unpkg.com/antd@5.12.8/dist/reset.css">
    <link rel="stylesheet" href="https://unpkg.com/antd@5.12.8/dist/antd.min.css">
    <link rel="stylesheet" href="https://at.alicdn.com/t/c/font_2975892_kbp2zx8rvn.css">
    <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/antd@5.12.8/dist/antd.min.js"></script>
    <script src="https://unpkg.com/rc-motion@2.9.0/lib/index.js"></script>
    <style>
        /* 主题变量 */
        :root {
            --primary-color: #11bde8;
            --success-color: #52c41a;
            --warning-color: #faad14;
            --error-color: #ff4d4f;
            --bg-color: #f0f2f5;
            --card-bg: #ffffff;
            --text-color: #262626;
            --text-secondary: #8c8c8c;
            --border-color: #d9d9d9;
            --shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            --radius: 6px;
            --header-bg: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
        }

        [data-theme="dark"] {
            --bg-color: #141414;
            --card-bg: #1f1f1f;
            --text-color: #ffffff;
            --text-secondary: #a6a6a6;
            --border-color: #434343;
            --shadow: 0 2px 8px rgba(0, 0, 0, 0.45);
            --header-bg: linear-gradient(135deg, #434343 0%, #000000 100%);
        }

        * {
            box-sizing: border-box;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            margin: 0;
            padding: 0;
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            line-height: 1.5715;
            font-size: 14px;
            min-height: 100vh;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .main-header {
            background: var(--header-bg);
            color: white;
            padding: 24px 0;
            text-align: center;
            box-shadow: var(--shadow);
            position: relative;
            overflow: hidden;
        }

        .main-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .main-header h1 {
            margin: 0;
            font-size: clamp(24px, 4vw, 32px);
            font-weight: 600;
            position: relative;
            z-index: 1;
        }

        .main-header p {
            margin: 8px 0 0 0;
            opacity: 0.9;
            font-size: clamp(12px, 2vw, 16px);
            position: relative;
            z-index: 1;
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px 16px;
            min-height: calc(100vh - 120px);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            padding: 24px;
            text-align: center;
            box-shadow: var(--shadow);
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transform: translateY(0);
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--success-color));
        }

        .stat-card .stat-number {
            font-size: clamp(20px, 3vw, 28px);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .stat-card .stat-label {
            color: var(--text-secondary);
            font-size: 14px;
            font-weight: 500;
        }

        .data-table-container {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            overflow: hidden;
            box-shadow: var(--shadow);
            margin-top: 24px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: var(--card-bg);
            font-size: 14px;
        }

        .data-table th {
            background: var(--bg-color);
            color: var(--text-color);
            font-weight: 600;
            padding: 16px 12px;
            text-align: left;
            border-bottom: 2px solid var(--border-color);
            white-space: nowrap;
        }

        .data-table td {
            color: var(--text-color);
            padding: 16px 12px;
            border-bottom: 1px solid var(--border-color);
            vertical-align: middle;
        }

        .data-table tbody tr {
            transition: background-color 0.2s;
        }

        .data-table tbody tr:hover {
            background-color: rgba(24, 144, 255, 0.05);
        }

        .data-table tbody tr:last-child td {
            border-bottom: none;
        }

        /* 移动端卡片式布局 */
        .mobile-card-container {
            display: none;
        }

        .mobile-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: var(--shadow);
        }

        .mobile-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border-color);
        }

        .mobile-card-phone {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color);
        }

        .mobile-card-balance {
            font-size: 16px;
            font-weight: 600;
            color: var(--success-color);
        }

        .mobile-card-body {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 13px;
        }

        .mobile-card-item {
            display: flex;
            flex-direction: column;
        }

        .mobile-card-label {
            color: var(--text-secondary);
            font-size: 11px;
            margin-bottom: 2px;
        }

        .mobile-card-value {
            color: var(--text-color);
            font-weight: 500;
        }

        .mobile-card-full {
            grid-column: 1 / -1;
        }

        /* 右上角工具栏 */
        .top-toolbar {
            position: fixed;
            top: 24px;
            right: 24px;
            display: flex;
            gap: 12px;
            z-index: 1000;
        }

        .top-toolbar-btn {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            border: none;
            background: var(--card-bg);
            color: var(--text-color);
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid var(--border-color);
        }

        .top-toolbar-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            background: var(--primary-color);
            color: white;
        }

        .top-toolbar-btn.logout-btn:hover {
            background: var(--error-color);
        }

        .login-status {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 12px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 12px;
            color: var(--success-color);
            box-shadow: var(--shadow);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .login-status:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .login-status svg {
            flex-shrink: 0;
        }

        /* 底部工具栏 */
        .toolbar {
            position: fixed;
            right: 24px;
            bottom: 24px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .toolbar-btn {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: var(--shadow);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 20px;
            color: white;
        }

        .toolbar-btn:hover {
            transform: translateY(-4px) scale(1.1);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
        }

        .toolbar-btn:active {
            transform: translateY(-2px) scale(1.05);
        }

        /* 回到顶部按钮 */
        .back-to-top {
            background: linear-gradient(135deg, var(--error-color), #ff7875);
            display: none;
        }

        /* 主题切换按钮 */
        .theme-toggle {
            background: linear-gradient(135deg, var(--warning-color), #ffc53d);
        }

        /* 查询按钮 */
        .query-btn-container {
            text-align: center;
            margin: 24px 0;
        }

        .query-btn {
            background: linear-gradient(135deg, var(--success-color), #73d13d);
            border: none;
            padding: 12px 32px;
            font-size: 16px;
            font-weight: 500;
            border-radius: 32px;
            color: white;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(82, 196, 26, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .query-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(82, 196, 26, 0.4);
        }

        .query-btn:active {
            transform: translateY(0);
        }

        /* 加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(4px);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-content {
            background: var(--card-bg);
            padding: 40px;
            border-radius: var(--radius);
            text-align: center;
            color: var(--text-color);
            box-shadow: var(--shadow);
            max-width: 300px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 登录页面样式 */
        .login-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            overflow: hidden;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 联动动画背景元素 */
        .login-bg-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .floating-element {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
        }

        .floating-element:nth-child(4) {
            width: 100px;
            height: 100px;
            top: 10%;
            right: 30%;
            animation-delay: 1s;
        }

        .floating-element:nth-child(5) {
            width: 70px;
            height: 70px;
            bottom: 20%;
            right: 40%;
            animation-delay: 3s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.7;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 1;
            }
        }

        /* 鼠标跟随效果 */
        .cursor-follower {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            pointer-events: none;
            transition: all 0.1s ease;
            z-index: 2;
        }

        .login-box {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 48px 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            width: 100%;
            max-width: 400px;
            margin: 20px;
            transform: translateY(20px);
            opacity: 0;
            animation: loginBoxFadeIn 0.6s ease forwards;
        }

        @keyframes loginBoxFadeIn {
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .login-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .login-header h1 {
            font-size: 28px;
            font-weight: 600;
            margin: 0 0 8px 0;
            color: var(--text-color);
        }

        .login-header p {
            color: var(--text-secondary);
            margin: 0;
            font-size: 14px;
        }

        .mode-switch {
            display: flex;
            background: var(--border-color);
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 20px;
        }

        .mode-btn {
            flex: 1;
            padding: 12px 20px;
            border: none;
            background: transparent;
            color: var(--text-secondary);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .mode-btn.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .mode-btn:hover:not(.active) {
            background: rgba(var(--primary-rgb), 0.1);
            color: var(--primary-color);
        }

        .login-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-group {
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 16px;
            background: var(--card-bg);
            color: var(--text-color);
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
        }

        .form-input::placeholder {
            color: var(--text-secondary);
        }

        .login-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, var(--primary-color), #40a9ff);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 8px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(24, 144, 255, 0.3);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* 自定义弹框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(4px);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            animation: modalFadeIn 0.3s ease;
        }

        @keyframes modalFadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        .modal-content {
            background: var(--card-bg);
            border-radius: 12px;
            padding: 32px;
            box-shadow: var(--shadow);
            max-width: 400px;
            width: 90%;
            transform: scale(0.9);
            animation: modalScaleIn 0.3s ease forwards;
        }

        @keyframes modalScaleIn {
            to {
                transform: scale(1);
            }
        }

        .modal-header {
            text-align: center;
            margin-bottom: 24px;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-color);
            margin: 0 0 8px 0;
        }

        .modal-subtitle {
            color: var(--text-secondary);
            font-size: 14px;
            margin: 0;
        }

        .modal-body {
            margin-bottom: 24px;
        }

        .modal-footer {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        .modal-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .modal-btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .modal-btn-primary:hover {
            background: #40a9ff;
            transform: translateY(-1px);
        }

        .modal-btn-secondary {
            background: var(--bg-color);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .modal-btn-secondary:hover {
            background: var(--border-color);
        }

        /* 进度条样式 */
        .progress-container {
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--bg-color);
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--success-color));
            border-radius: 4px;
            transition: width 0.3s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: progressShine 2s infinite;
        }

        @keyframes progressShine {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        .progress-text {
            text-align: center;
            margin-top: 8px;
            font-size: 14px;
            color: var(--text-secondary);
        }

        /* 自动查询设置样式 */
        .auto-query-container {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            padding: 20px;
            margin-bottom: 24px;
            box-shadow: var(--shadow);
        }

        .auto-query-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .auto-query-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color);
            margin: 0;
        }

        .auto-query-controls {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .auto-query-interval-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .auto-query-label {
            font-size: 14px;
            color: var(--text-color);
            font-weight: 500;
        }

        .auto-query-select {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--card-bg);
            color: var(--text-color);
            font-size: 14px;
            cursor: pointer;
        }

        .auto-query-status {
            font-size: 14px;
            color: var(--text-secondary);
            margin-top: 8px;
        }

        .auto-query-status.active {
            color: var(--success-color);
        }

        /* 响应式设计 */
        @media (max-width: 1400px) {
            .main-container {
                max-width: 100%;
                padding: 20px 16px;
            }
        }

        @media (max-width: 1024px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;
            }

            .data-table-container {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            .data-table {
                min-width: 800px;
                font-size: 13px;
            }

            .data-table th,
            .data-table td {
                padding: 12px 10px;
                white-space: nowrap;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 16px 12px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }

            .stat-card {
                padding: 18px 14px;
            }

            .stat-card .stat-number {
                font-size: clamp(18px, 4vw, 24px);
            }

            .toolbar {
                right: 16px;
                bottom: 16px;
            }

            .toolbar-btn {
                width: 48px;
                height: 48px;
                font-size: 18px;
            }

            /* 在平板上仍使用表格 */
            .data-table-container {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            .data-table {
                min-width: 700px;
                font-size: 12px;
            }

            .data-table th,
            .data-table td {
                padding: 10px 8px;
            }

            /* 自动查询控制面板优化 */
            .auto-query-controls {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }

            .auto-query-controls > * {
                width: 100%;
            }

            .auto-query-select {
                width: auto;
            }
        }

        @media (max-width: 480px) {
            .main-header {
                padding: 16px 0;
            }

            .main-header h1 {
                font-size: clamp(20px, 5vw, 28px);
            }

            .main-header p {
                font-size: clamp(11px, 3vw, 14px);
            }

            .stats-grid {
                grid-template-columns: 1fr 1fr;
                gap: 8px;
            }

            .stat-card {
                padding: 14px 10px;
            }

            .stat-card .stat-number {
                font-size: clamp(16px, 4vw, 20px);
                margin-bottom: 6px;
            }

            .stat-card .stat-label {
                font-size: 12px;
            }

            .query-btn {
                padding: 10px 20px;
                font-size: 14px;
            }

            .toolbar {
                right: 12px;
                bottom: 12px;
            }

            .toolbar-btn {
                width: 44px;
                height: 44px;
                font-size: 16px;
            }

            /* 移动端使用卡片布局 */
            .data-table-container {
                display: none;
            }

            .mobile-card-container {
                display: block;
                margin-top: 24px;
            }

            /* 移动端模态框优化 */
            .modal-content {
                margin: 20px;
                padding: 24px;
            }

            .login-box {
                margin: 16px;
                padding: 32px 24px;
            }

            /* 自动查询控制面板移动端优化 */
            .auto-query-container {
                padding: 16px;
            }

            .auto-query-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .auto-query-controls {
                width: 100%;
                flex-direction: row;
                flex-wrap: wrap;
                gap: 12px;
                align-items: center;
            }

            .auto-query-toggle {
                flex-shrink: 0;
            }

            .auto-query-select {
                flex-shrink: 0;
                min-width: 80px;
                max-width: 120px;
            }

            .auto-query-status {
                flex: 1;
                min-width: 100%;
                text-align: left;
                margin-top: 8px;
            }
        }

        /* 超小屏幕优化 */
        @media (max-width: 360px) {
            .main-container {
                padding: 12px 8px;
            }

            .stats-grid {
                gap: 6px;
            }

            .stat-card {
                padding: 12px 8px;
            }

            .data-table {
                min-width: 550px;
                font-size: 10px;
            }

            .data-table th,
            .data-table td {
                padding: 6px 4px;
            }
        }
    </style>
</head>
<body>
    <!-- 登录页面 -->
    <div class="login-container" id="loginContainer">
        <!-- 背景动画元素 -->
        <div class="login-bg-elements">
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
        </div>
        <!-- 鼠标跟随效果 -->
        <div class="cursor-follower" id="cursorFollower"></div>

        <div class="login-box">
            <div class="login-header">
                <h1>自助查询系统</h1>
                <p id="loginSubtitle">请登录以继续使用</p>
            </div>

            <!-- 模式切换按钮 -->
            <div class="mode-switch" id="modeSwitch">
                <button type="button" class="mode-btn active" id="loginModeBtn">登录</button>
                <button type="button" class="mode-btn" id="registerModeBtn">注册</button>
            </div>

            <!-- 登录表单 -->
            <form class="login-form" id="loginForm">
                <div class="form-group">
                    <input type="text" class="form-input" id="loginUsername" placeholder="用户名" required>
                </div>
                <div class="form-group">
                    <input type="password" class="form-input" id="loginPassword" placeholder="密码" required>
                </div>
                <button type="submit" class="login-btn" id="loginBtn">
                    <span id="loginBtnText">登录</span>
                    <div class="loading-spinner" id="loginSpinner" style="display: none; width: 20px; height: 20px; margin: 0 auto;"></div>
                </button>
            </form>

            <!-- 注册表单 -->
            <form class="login-form" id="registerForm" style="display: none;">
                <div class="form-group">
                    <input type="text" class="form-input" id="registerUsername" placeholder="用户名（至少3个字符）" required>
                </div>
                <div class="form-group">
                    <input type="password" class="form-input" id="registerPassword" placeholder="密码（至少6个字符）" required>
                </div>
                <div class="form-group">
                    <input type="password" class="form-input" id="confirmPassword" placeholder="确认密码" required>
                </div>
                <button type="submit" class="login-btn" id="registerBtn">
                    <span id="registerBtnText">注册</span>
                    <div class="loading-spinner" id="registerSpinner" style="display: none; width: 20px; height: 20px; margin: 0 auto;"></div>
                </button>
            </form>
        </div>
    </div>

    <!-- 主应用页面 -->
    <div class="main-app" id="mainApp" style="display: none;">
        <!-- 右上角工具栏 -->
        <div class="top-toolbar">
            <div class="login-status" id="loginStatus" title="登录状态">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                <span id="loginStatusText">已登录</span>
            </div>
            <button class="top-toolbar-btn logout-btn" id="logoutBtn" title="退出登录">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
                </svg>
            </button>
            <button class="top-toolbar-btn" id="themeToggle" title="切换主题">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 8.69V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12 20 8.69zM12 18c-3.31 0-6-2.69-6 6-6s2.69-6 6-6 6 2.69 6 6-2.69 6-6 6zm0-10c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4z"/>
                </svg>
            </button>
        </div>

        <!-- 头部 -->
        <div class="main-header">
            <h1>自助查询系统</h1>
            <p>查询管理系统</p>
        </div>

    <!-- 主容器 -->
    <div class="main-container">
        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card" data-aos="fade-up" data-aos-delay="100">
                <div class="stat-number" id="totalAccounts">0</div>
                <div class="stat-label">总账号数</div>
            </div>
            <div class="stat-card" data-aos="fade-up" data-aos-delay="200">
                <div class="stat-number" id="totalBalance">0.00元</div>
                <div class="stat-label">总余额</div>
            </div>
            <div class="stat-card" data-aos="fade-up" data-aos-delay="300">
                <div class="stat-number" id="totalVouchers">0元</div>
                <div class="stat-label">话费券总额</div>
            </div>
            <div class="stat-card" data-aos="fade-up" data-aos-delay="400">
                <div class="stat-number" id="lastUpdate">--</div>
                <div class="stat-label">最后更新</div>
            </div>
        </div>

        <!-- 自动查询设置 -->
        <div class="auto-query-container" data-aos="fade-up" data-aos-delay="450">
            <div class="auto-query-header">
                <h3 class="auto-query-title">自动查询设置</h3>
            </div>
            <div class="auto-query-controls">
                <div class="auto-query-interval-group">
                    <label for="autoQueryInterval" class="auto-query-label">查询间隔：</label>
                    <select class="auto-query-select" id="autoQueryInterval">
                        <option value="30">30分钟</option>
                        <option value="60" selected>1小时</option>
                        <option value="120">2小时</option>
                        <option value="180">3小时</option>
                        <option value="360">6小时</option>
                        <option value="720">12小时</option>
                    </select>
                </div>
                <span id="autoQueryStatus" class="auto-query-status active">下次查询: 计算中...</span>
            </div>
        </div>

        <!-- 查询按钮 -->
        <div class="query-btn-container" data-aos="fade-up" data-aos-delay="500">
            <button class="query-btn" id="queryBtn">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
                手动查询
            </button>
        </div>

        <!-- 数据表格 -->
        <div class="data-table-container" data-aos="fade-up" data-aos-delay="600">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>手机号</th>
                        <th>当前余额</th>
                        <th>历史累计</th>
                        <th>话费券</th>
                        <th>任务状态</th>
                        <th>最后查询</th>
                    </tr>
                </thead>
                <tbody id="dataTableBody">
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 60px 20px; color: var(--text-secondary);">
                            <div style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;">📊</div>
                            <div style="font-size: 16px; margin-bottom: 8px;">暂无数据</div>
                            <div style="font-size: 14px;">请点击"手动查询"获取数据</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 移动端卡片布局 -->
        <div class="mobile-card-container" data-aos="fade-up" data-aos-delay="600">
            <div id="mobileCardBody">
                <div style="text-align: center; padding: 60px 20px; color: var(--text-secondary);">
                    <div style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;">📊</div>
                    <div style="font-size: 16px; margin-bottom: 8px;">暂无数据</div>
                    <div style="font-size: 14px;">请点击"手动查询"获取数据</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
        <button class="toolbar-btn back-to-top" id="backToTop" title="回到顶部" style="display: none;">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z"/>
            </svg>
        </button>
    </div>

    <!-- 查询进度弹框 -->
    <div class="modal-overlay" id="queryModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">正在查询</h3>
                <p class="modal-subtitle">请稍候，正在获取最新数据...</p>
            </div>
            <div class="modal-body">
                <div class="progress-container" id="queryProgress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill" style="width: 0%;"></div>
                    </div>
                    <div class="progress-text" id="progressText">准备查询...</div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" onclick="closeQueryModal()">取消</button>
            </div>
        </div>
    </div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div style="font-size: 16px; font-weight: 500;">正在查询数据</div>
            <div style="font-size: 14px; color: var(--text-secondary); margin-top: 8px;">请稍候...</div>
        </div>
    </div>
    </div>

    <!-- AOS 动画库 -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script>
        // 初始化AOS动画
        AOS.init({
            duration: 800,
            easing: 'ease-out-cubic',
            once: true,
            offset: 100
        });

        // 全局变量
        window.appData = {
            isDarkMode: localStorage.getItem('darkMode') === 'true',
            isLoggedIn: checkLoginStatus(), // 检查登录状态和有效期
            autoQueryEnabled: true, // 默认开启，不可关闭
            autoQueryInterval: parseInt(localStorage.getItem('autoQueryInterval')) || 60, // 默认1小时
            autoQueryTimer: null,
            countdownTimer: null,
            nextQueryTime: null,
            loginExpireHours: 7 * 24, // 登录有效期：7天
            hasRegisteredUser: false  // 是否已有注册用户
        };

        // 检查登录状态和有效期
        function checkLoginStatus() {
            const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
            const loginTime = localStorage.getItem('loginTime');

            if (!isLoggedIn || !loginTime) {
                return false;
            }

            // 检查登录是否过期（365天有效期）
            const loginTimestamp = parseInt(loginTime);
            const now = Date.now();
            const expireTime = 365 * 24 * 60 * 60 * 1000; // 365天（毫秒）

            if (now - loginTimestamp > expireTime) {
                // 登录已过期，清除登录状态
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('loginTime');
                console.log('登录已过期，需要重新登录');
                return false;
            }

            console.log('登录状态有效，剩余时间：', Math.round((expireTime - (now - loginTimestamp)) / (1000 * 60 * 60 * 24)), '天');
            return true;
        }

        // 更新登录状态显示
        function updateLoginStatus() {
            const loginStatusEl = document.getElementById('loginStatus');
            const loginStatusTextEl = document.getElementById('loginStatusText');

            if (!loginStatusEl || !loginStatusTextEl) return;

            const loginTime = localStorage.getItem('loginTime');
            if (!loginTime) {
                loginStatusTextEl.textContent = '未登录';
                return;
            }

            const loginTimestamp = parseInt(loginTime);
            const now = Date.now();
            const expireTime = 7 * 24 * 60 * 60 * 1000; // 7天
            const remainingTime = expireTime - (now - loginTimestamp);

            if (remainingTime > 0) {
                const days = Math.floor(remainingTime / (1000 * 60 * 60 * 24));
                const hours = Math.floor((remainingTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

                if (days > 0) {
                    loginStatusTextEl.textContent = `${days}天`;
                } else if (hours > 0) {
                    loginStatusTextEl.textContent = `${hours}小时`;
                } else {
                    const minutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60));
                    loginStatusTextEl.textContent = `${minutes}分钟`;
                }

                // 设置tooltip显示详细信息
                const expireDate = new Date(loginTimestamp + expireTime);
                loginStatusEl.title = `登录有效期至: ${expireDate.toLocaleString()}`;
            } else {
                loginStatusTextEl.textContent = '已过期';
            }
        }

        // 模式切换功能
        function switchMode(mode) {
            const loginForm = document.getElementById('loginForm');
            const registerForm = document.getElementById('registerForm');
            const loginModeBtn = document.getElementById('loginModeBtn');
            const registerModeBtn = document.getElementById('registerModeBtn');
            const subtitle = document.getElementById('loginSubtitle');

            if (mode === 'login') {
                loginForm.style.display = 'flex';
                registerForm.style.display = 'none';
                loginModeBtn.classList.add('active');
                registerModeBtn.classList.remove('active');
                subtitle.textContent = '请登录以继续使用';
            } else {
                loginForm.style.display = 'none';
                registerForm.style.display = 'flex';
                loginModeBtn.classList.remove('active');
                registerModeBtn.classList.add('active');
                subtitle.textContent = '创建新账户';
            }
        }

        // 检查注册状态
        async function checkRegistrationStatus() {
            try {
                const response = await fetch('/api/check-registration');
                const data = await response.json();

                if (data.success && data.has_registered_user) {
                    // 已有用户注册，完全隐藏注册功能
                    const modeSwitch = document.getElementById('modeSwitch');
                    const registerModeBtn = document.getElementById('registerModeBtn');
                    const registerForm = document.getElementById('registerForm');

                    // 隐藏注册按钮和表单
                    registerModeBtn.style.display = 'none';
                    registerForm.style.display = 'none';

                    // 如果当前在注册模式，强制切换到登录模式
                    if (registerModeBtn.classList.contains('active')) {
                        switchMode('login');
                    }

                    // 标记系统已有用户
                    window.appData.hasRegisteredUser = true;

                    console.log('系统已有注册用户，注册功能已禁用');
                } else {
                    window.appData.hasRegisteredUser = false;
                }
            } catch (error) {
                console.error('检查注册状态失败:', error);
                // 出错时默认禁用注册功能，确保安全
                window.appData.hasRegisteredUser = true;
            }
        }

        // 处理注册
        async function handleRegister(event) {
            event.preventDefault();

            // 首先检查是否已有用户注册（前端双重检查）
            if (window.appData.hasRegisteredUser) {
                showMessage('系统已有用户注册，禁止重复注册', 'error');
                return;
            }

            // 实时检查注册状态
            try {
                const checkResponse = await fetch('/api/check-registration');
                const checkData = await checkResponse.json();
                if (checkData.success && checkData.has_registered_user) {
                    showMessage('系统已有用户注册，禁止重复注册', 'error');
                    // 更新状态并隐藏注册功能
                    await checkRegistrationStatus();
                    return;
                }
            } catch (error) {
                console.error('检查注册状态失败:', error);
                showMessage('无法验证注册状态，请刷新页面重试', 'error');
                return;
            }

            const username = document.getElementById('registerUsername').value.trim();
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            // 验证输入
            if (username.length < 3) {
                showMessage('用户名至少需要3个字符', 'error');
                return;
            }

            if (password.length < 6) {
                showMessage('密码至少需要6个字符', 'error');
                return;
            }

            if (password !== confirmPassword) {
                showMessage('两次输入的密码不一致', 'error');
                return;
            }

            const registerBtn = document.getElementById('registerBtn');
            const registerBtnText = document.getElementById('registerBtnText');
            const registerSpinner = document.getElementById('registerSpinner');

            // 显示加载状态
            registerBtn.disabled = true;
            registerBtnText.style.display = 'none';
            registerSpinner.style.display = 'block';

            try {
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showMessage('注册成功！请登录', 'success');

                    // 清空注册表单
                    document.getElementById('registerForm').reset();

                    // 切换到登录模式
                    switchMode('login');

                    // 完全隐藏注册功能
                    const registerModeBtn = document.getElementById('registerModeBtn');
                    const registerForm = document.getElementById('registerForm');
                    registerModeBtn.style.display = 'none';
                    registerForm.style.display = 'none';

                    // 更新状态
                    window.appData.hasRegisteredUser = true;

                    // 填充用户名到登录表单
                    document.getElementById('loginUsername').value = username;

                    console.log('注册成功，注册功能已永久禁用');
                } else {
                    showMessage(data.message || '注册失败', 'error');

                    // 如果是因为已有用户而失败，更新状态
                    if (data.message && data.message.includes('已有用户')) {
                        await checkRegistrationStatus();
                    }
                }
            } catch (error) {
                console.error('注册请求失败:', error);
                showMessage('注册请求失败，请检查网络连接', 'error');
            } finally {
                // 恢复按钮状态
                registerBtn.disabled = false;
                registerBtnText.style.display = 'inline';
                registerSpinner.style.display = 'none';
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            init();
            checkRegistrationStatus(); // 检查注册状态
        });

        function init() {
            // 设置主题
            setTheme(window.appData.isDarkMode);

            // 检查登录状态
            if (window.appData.isLoggedIn) {
                showMainApp();
            } else {
                showLoginPage();
            }

            // 绑定事件
            bindEvents();

            // 添加页面加载动画
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        }

        function showLoginPage() {
            document.getElementById('loginContainer').style.display = 'flex';
            document.getElementById('mainApp').style.display = 'none';
        }

        function showMainApp() {
            document.getElementById('loginContainer').style.display = 'none';
            document.getElementById('mainApp').style.display = 'block';

            // 更新登录状态显示
            updateLoginStatus();

            // 每分钟更新一次登录状态显示
            setInterval(updateLoginStatus, 60000);

            // 初始化自动查询设置
            initAutoQuery();

            // 加载初始数据
            loadData();

            // 监听滚动
            window.addEventListener('scroll', handleScroll);
        }

        function bindEvents() {
            // 登录表单
            document.getElementById('loginForm').addEventListener('submit', handleLogin);

            // 注册表单
            document.getElementById('registerForm').addEventListener('submit', handleRegister);

            // 模式切换按钮
            document.getElementById('loginModeBtn').addEventListener('click', () => switchMode('login'));
            document.getElementById('registerModeBtn').addEventListener('click', () => switchMode('register'));

            // 主题切换
            document.getElementById('themeToggle').addEventListener('click', toggleTheme);

            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', logout);

            // 回到顶部
            document.getElementById('backToTop').addEventListener('click', scrollToTop);

            // 查询按钮 - 直接执行查询
            document.getElementById('queryBtn').addEventListener('click', startDirectQuery);

            // 自动查询间隔选择
            document.getElementById('autoQueryInterval').addEventListener('change', updateAutoQueryInterval);

            // 登录页面鼠标跟随效果
            initLoginAnimations();
        }

        // 处理登录
        function handleLogin(e) {
            e.preventDefault();

            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;
            const loginBtn = document.getElementById('loginBtn');
            const loginBtnText = document.getElementById('loginBtnText');
            const loginSpinner = document.getElementById('loginSpinner');

            // 显示加载状态
            loginBtn.disabled = true;
            loginBtnText.style.display = 'none';
            loginSpinner.style.display = 'block';

            // 调用服务器端登录API
            fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 登录成功，保存登录状态和时间戳
                    localStorage.setItem('isLoggedIn', 'true');
                    localStorage.setItem('loginTime', Date.now().toString());
                    window.appData.isLoggedIn = true;

                    showMessage('登录成功！长期有效', 'success');

                    setTimeout(() => {
                        showMainApp();
                    }, 500);
                } else {
                    // 登录失败
                    showMessage(data.message || '登录失败！', 'error');

                    // 恢复按钮状态
                    loginBtn.disabled = false;
                    loginBtnText.style.display = 'inline';
                    loginSpinner.style.display = 'none';

                    // 清空密码
                    document.getElementById('loginPassword').value = '';
                }
            })
            .catch(error => {
                console.error('登录请求失败:', error);
                showMessage('网络错误，请重试！', 'error');

                // 恢复按钮状态
                loginBtn.disabled = false;
                loginBtnText.style.display = 'inline';
                loginSpinner.style.display = 'none';
            });
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('loginTime');
            window.appData.isLoggedIn = false;
            showLoginPage();
            showMessage('已退出登录', 'info');
        }

        // 主题切换
        function toggleTheme() {
            window.appData.isDarkMode = !window.appData.isDarkMode;
            setTheme(window.appData.isDarkMode);
            localStorage.setItem('darkMode', window.appData.isDarkMode);

            // 添加切换动画
            document.body.style.transition = 'all 0.3s ease';
            setTimeout(() => {
                document.body.style.transition = '';
            }, 300);
        }

        function setTheme(isDark) {
            const themeIcon = document.querySelector('#themeToggle svg');
            if (isDark) {
                document.documentElement.setAttribute('data-theme', 'dark');
                themeIcon.innerHTML = '<path d="M12 7c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5-2.24-5-5-5zM2 13h2c.55 0 1-.45 1-1s-.45-1-1-1H2c-.55 0-1 .45-1 1s.45 1 1 1zm18 0h2c.55 0 1-.45 1-1s-.45-1-1-1h-2c-.55 0-1 .45-1 1s.45 1 1 1zM11 2v2c0 .55.45 1 1 1s1-.45 1-1V2c0-.55-.45-1-1-1s-1 .45-1 1zm0 18v2c0 .55.45 1 1 1s1-.45 1-1v-2c0-.55-.45-1-1-1s-1 .45-1 1zM5.99 4.58c-.39-.39-1.03-.39-1.41 0-.39.39-.39 1.03 0 1.41l1.06 1.06c.39.39 1.03.39 1.41 0s.39-1.03 0-1.41L5.99 4.58zm12.37 12.37c-.39-.39-1.03-.39-1.41 0-.39.39-.39 1.03 0 1.41l1.06 1.06c.39.39 1.03.39 1.41 0 .39-.39.39-1.03 0-1.41l-1.06-1.06zm1.06-10.96c.39-.39.39-1.03 0-1.41-.39-.39-1.03-.39-1.41 0l-1.06 1.06c-.39.39-.39 1.03 0 1.41s1.03.39 1.41 0l1.06-1.06zM7.05 18.36c.39-.39.39-1.03 0-1.41-.39-.39-1.03-.39-1.41 0l-1.06 1.06c-.39.39-.39 1.03 0 1.41s1.03.39 1.41 0l1.06-1.06z"/>';
            } else {
                document.documentElement.removeAttribute('data-theme');
                themeIcon.innerHTML = '<path d="M12 18c-.89 0-1.74-.19-2.5-.54C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.46C10.26 6.19 11.11 6 12 6c3.31 0 6 2.69 6 6s-2.69 6-6 6z"/>';
            }
        }

            // 滚动处理
            function handleScroll() {
                const backToTopBtn = document.getElementById('backToTop');
                if (window.pageYOffset > 300) {
                    backToTopBtn.style.display = 'flex';
                } else {
                    backToTopBtn.style.display = 'none';
                }
            }

            // 回到顶部
            function scrollToTop() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }

        // 初始化自动查询
        function initAutoQuery() {
            // 设置UI状态
            document.getElementById('autoQueryInterval').value = window.appData.autoQueryInterval;

            // 获取后端自动查询状态
            updateAutoQueryStatus();
        }

        // 更新自动查询间隔
        function updateAutoQueryInterval() {
            const intervalMinutes = parseInt(document.getElementById('autoQueryInterval').value);
            window.appData.autoQueryInterval = intervalMinutes;
            localStorage.setItem('autoQueryInterval', intervalMinutes);

            // 通过API设置后端定时间隔
            fetch('/api/set_auto_query_interval', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    interval_minutes: intervalMinutes
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(`自动查询间隔已更新为 ${intervalMinutes} 分钟`, 'success');
                    updateAutoQueryStatus();
                } else {
                    showMessage('设置失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('设置自动查询间隔失败:', error);
                showMessage('设置自动查询间隔失败', 'error');
            });
        }

        // 启动自动查询（已移除，由后端处理）
        function startAutoQuery() {
            // 自动查询现在由后端处理，这里只更新状态显示
            updateAutoQueryStatus();
        }

        // 停止自动查询（已移除，由后端处理）
        function stopAutoQuery() {
            // 自动查询现在由后端处理，这里只清理前端状态
            if (window.appData.countdownTimer) {
                clearInterval(window.appData.countdownTimer);
                window.appData.countdownTimer = null;
            }
        }

        // 执行自动查询（已移除，由后端处理）
        // 自动查询现在完全由后端定时执行，前端不再需要此函数

        // 轮询自动查询状态（已移除，由后端处理）
        // 自动查询现在完全由后端定时执行，前端不再需要轮询状态

        // 更新自动查询状态显示
        function updateAutoQueryStatus() {
            const statusEl = document.getElementById('autoQueryStatus');

            // 从后端获取自动查询状态
            fetch('/api/get_auto_query_status')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data) {
                        const status = data.data;

                        if (status.is_running) {
                            // 检查是否正在查询
                            if (status.is_querying) {
                                statusEl.textContent = '正在查询...';
                                statusEl.className = 'auto-query-status active';
                                // 如果正在查询，更频繁地检查状态
                                if (!window.appData.countdownTimer) {
                                    window.appData.countdownTimer = setInterval(updateAutoQueryStatus, 2000);
                                }
                                return;
                            }

                            if (status.next_query_time) {
                                const nextTime = new Date(status.next_query_time);
                                const now = new Date();
                                const diff = nextTime - now;

                                if (diff > 0) {
                                    const hours = Math.floor(diff / (1000 * 60 * 60));
                                    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
                                    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

                                    let timeText = '';
                                    if (hours > 0) {
                                        timeText = `${hours}小时${minutes}分${seconds}秒后`;
                                    } else if (minutes > 0) {
                                        timeText = `${minutes}分${seconds}秒后`;
                                    } else {
                                        timeText = `${seconds}秒后`;
                                    }

                                    statusEl.textContent = `下次查询: ${timeText}`;

                                    // 设置定时器更新倒计时（每10秒更新一次，减少频率）
                                    if (!window.appData.countdownTimer) {
                                        window.appData.countdownTimer = setInterval(updateAutoQueryStatus, 10000);
                                    }
                                } else {
                                    statusEl.textContent = '正在查询...';
                                    // 如果正在查询，更频繁地检查状态
                                    if (!window.appData.countdownTimer) {
                                        window.appData.countdownTimer = setInterval(updateAutoQueryStatus, 2000);
                                    }
                                }
                            } else {
                                statusEl.textContent = `自动查询已启动 (间隔: ${status.interval_minutes}分钟)`;
                            }
                        } else {
                            statusEl.textContent = '自动查询已停止';
                        }

                        statusEl.className = 'auto-query-status active';
                    } else {
                        statusEl.textContent = '状态获取失败';
                        statusEl.className = 'auto-query-status';
                    }
                })
                .catch(error => {
                    console.error('获取自动查询状态失败:', error);
                    statusEl.textContent = '状态获取失败';
                    statusEl.className = 'auto-query-status';
                });
        }

        // 直接开始查询（无需密码）
        function startDirectQuery() {
            // 显示查询进度弹框
            document.getElementById('queryModal').style.display = 'flex';

            // 显示进度条
            document.getElementById('queryProgress').style.display = 'block';
            document.getElementById('progressFill').style.width = '0%';
            document.getElementById('progressText').textContent = '准备查询...';

            // 直接调用查询API（无需密码）
            fetch('/api/query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    type: 'manual'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 轮询查询状态，显示真实进度
                    pollQueryStatusWithProgress(data.query_id);
                } else {
                    throw new Error(data.message || '查询失败');
                }
            })
            .catch(error => {
                console.log('API调用失败:', error.message);
                showMessage(error.message || '查询失败', 'error');
                closeQueryModal();
            });
        }

        // 显示查询弹框（已弃用）
        function showQueryModal() {
            document.getElementById('queryModal').style.display = 'flex';
            document.getElementById('queryPassword').focus();

            // 重置进度条
            document.getElementById('queryProgress').style.display = 'none';
            document.getElementById('progressFill').style.width = '0%';
            document.getElementById('progressText').textContent = '准备查询...';
        }

        // 关闭查询弹框
        function closeQueryModal() {
            document.getElementById('queryModal').style.display = 'none';
        }

        // 确认查询
        function confirmQuery() {
            const password = document.getElementById('queryPassword').value;

            if (!password) {
                showMessage('请输入查询密码！', 'warning');
                return;
            }

            // 显示进度条
            document.getElementById('queryProgress').style.display = 'block';

            // 直接开始查询，密码验证在服务器端进行
            startQueryWithPassword(password);
        }

        // 带密码的查询
        function startQueryWithPassword(password) {
            // 尝试调用真实API
            fetch('/api/query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    password: password,
                    type: 'manual'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 轮询查询状态，显示真实进度
                    pollQueryStatusWithProgress(data.query_id);
                } else {
                    throw new Error(data.message || '查询失败');
                }
            })
            .catch(error => {
                console.log('API调用失败:', error.message);
                showMessage(error.message || '查询失败', 'error');

                // 隐藏进度条
                document.getElementById('queryProgress').style.display = 'none';
            });
        }

        // 轮询查询状态并显示进度
        function pollQueryStatusWithProgress(queryId) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            const poll = () => {
                fetch('/api/query_status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query_id: queryId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data) {
                        const status = data.data;

                        // 更新进度条
                        progressFill.style.width = status.progress + '%';

                        // 构建进度文本
                        let progressMessage = status.message;
                        if (status.total_accounts && status.processed_accounts !== undefined) {
                            progressMessage += ` (${status.processed_accounts}/${status.total_accounts})`;
                        }
                        progressMessage += ` ${status.progress}%`;

                        progressText.textContent = progressMessage;

                        if (status.status === 'completed') {
                            // 查询完成
                            progressText.textContent = `查询完成！ (${status.total_accounts}/${status.total_accounts}) 100%`;
                            setTimeout(() => {
                                closeQueryModal();
                                loadData();
                                showMessage('查询完成！', 'success');
                            }, 1000);
                        } else if (status.status === 'failed') {
                            // 查询失败
                            showMessage('查询失败: ' + (status.message || '未知错误'), 'error');
                            closeQueryModal();
                        } else {
                            // 继续轮询
                            setTimeout(poll, 1000);
                        }
                    } else {
                        throw new Error(data.message || '状态查询失败');
                    }
                })
                .catch(error => {
                    console.log('状态查询失败:', error.message);
                    showMessage('查询失败', 'error');
                    closeQueryModal();
                });
            };

            poll();
        }

        // 显示消息提示
        function showMessage(message, type = 'info') {
            const messageEl = document.createElement('div');
            messageEl.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            `;

            switch(type) {
                case 'success':
                    messageEl.style.background = 'linear-gradient(135deg, #52c41a, #73d13d)';
                    break;
                case 'error':
                    messageEl.style.background = 'linear-gradient(135deg, #ff4d4f, #ff7875)';
                    break;
                case 'warning':
                    messageEl.style.background = 'linear-gradient(135deg, #faad14, #ffc53d)';
                    break;
                default:
                    messageEl.style.background = 'linear-gradient(135deg, #1890ff, #40a9ff)';
            }

            messageEl.textContent = message;
            document.body.appendChild(messageEl);

            // 显示动画
            setTimeout(() => {
                messageEl.style.transform = 'translateX(0)';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                messageEl.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (messageEl.parentNode) {
                        messageEl.parentNode.removeChild(messageEl);
                    }
                }, 300);
            }, 3000);
        }

        // 开始查询（已弃用，使用startQueryWithPassword代替）
        function startQuery() {
            showLoading(true);

            // 尝试调用真实API（移除密码验证）
            fetch('/api/query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    type: 'manual'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 轮询查询状态
                    pollQueryStatus(data.query_id);
                } else {
                    throw new Error(data.message || '查询失败');
                }
            })
            .catch(error => {
                console.log('API调用失败，使用模拟数据:', error.message);
                // 模拟查询过程
                setTimeout(() => {
                    loadMockData();
                    showLoading(false);
                    showMessage('查询完成！', 'success');
                }, 3000);
            });
        }

        // 轮询查询状态
        function pollQueryStatus(queryId) {
            const poll = () => {
                fetch('/api/query_status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query_id: queryId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data) {
                        const status = data.data;
                        if (status.status === 'completed') {
                            showLoading(false);
                            loadData();
                            showMessage('查询完成！', 'success');
                        } else if (status.status === 'failed') {
                            showLoading(false);
                            showMessage('查询失败: ' + (status.error || '未知错误'), 'error');
                        } else {
                            // 继续轮询
                            setTimeout(poll, 2000);
                        }
                    } else {
                        throw new Error(data.message || '状态查询失败');
                    }
                })
                .catch(error => {
                    console.log('状态查询失败:', error.message);
                    showLoading(false);
                    loadMockData();
                    showMessage('查询完成！', 'success');
                });
            };

            poll();
        }

            // 显示/隐藏加载
            function showLoading(show) {
                const overlay = document.getElementById('loadingOverlay');
                overlay.style.display = show ? 'flex' : 'none';
            }

            // 加载数据
            function loadData() {
                // 尝试从API加载数据，失败则显示空状态
                fetch('/api/data')
                    .then(response => response.json())
                    .then(data => {
                        updateStats(data);
                        updateTable(data);
                    })
                    .catch(() => {
                        // API不可用时显示空状态
                        console.log('API不可用，显示空状态');
                    });
            }

            // 加载模拟数据
            function loadMockData() {
                const mockData = [
                    {
                        phone: '16685105259',
                        balance: '0.86',
                        total: '79.62',
                        vouchers: '',
                        tasks: '',
                        lastQuery: '2025-06-10 22:01:29'
                    },
                    {
                        phone: '18845105658',
                        balance: '1.29',
                        total: '95.23',
                        vouchers: '',
                        tasks: '',
                        lastQuery: '2025-06-10 22:01:33'
                    },
                    {
                        phone: '15326426683',
                        balance: '1.33',
                        total: '72.01',
                        vouchers: '5元券(20250704)',
                        tasks: '阅读120分钟: 120/120',
                        lastQuery: '2025-06-10 22:01:37'
                    },
                    {
                        phone: '17771074627',
                        balance: '3.13',
                        total: '80.27',
                        vouchers: '',
                        tasks: '阅读240分钟: 240/240',
                        lastQuery: '2025-06-10 22:01:51'
                    },
                    {
                        phone: '***********',
                        balance: '2.54',
                        total: '73.70',
                        vouchers: '5元券(********)',
                        tasks: '阅读240分钟: 240/240',
                        lastQuery: '2025-06-10 22:02:03'
                    }
                ];

                updateStats(mockData);
                updateTable(mockData);
            }

            // 更新统计
            function updateStats(data) {
                const totalAccounts = data.length;
                const totalBalance = data.reduce((sum, item) => sum + parseFloat(item.balance || 0), 0);

                let totalVouchers = 0;
                data.forEach(item => {
                    if (item.vouchers && item.vouchers.trim()) {
                        // 处理HTML格式的话费券数据
                        let vouchersText = item.vouchers;

                        // 移除HTML标签，获取纯文本
                        vouchersText = vouchersText.replace(/<br>/g, '\n').replace(/<[^>]*>/g, '');

                        // 方法1：查找"总未使用金额"行
                        const totalMatch = vouchersText.match(/总未使用金额[：:]\s*(\d+)元/);
                        if (totalMatch) {
                            const amount = parseInt(totalMatch[1]);
                            if (!isNaN(amount)) {
                                totalVouchers += amount;
                            }
                        } else {
                            // 方法2：如果没有总金额，解析每行话费券
                            const lines = vouchersText.split('\n');
                            lines.forEach(line => {
                                line = line.trim();
                                // 匹配格式：数字/日期 或 数字元券
                                const voucherMatch = line.match(/^(\d+)[\/元]/);
                                if (voucherMatch) {
                                    const amount = parseInt(voucherMatch[1]);
                                    if (!isNaN(amount)) {
                                        totalVouchers += amount;
                                    }
                                }
                            });
                        }
                    }
                });

                document.getElementById('totalAccounts').textContent = totalAccounts;
                document.getElementById('totalBalance').textContent = totalBalance.toFixed(2) + '元';
                document.getElementById('totalVouchers').textContent = totalVouchers + '元';
                document.getElementById('lastUpdate').textContent = new Date().toLocaleString();
            }

        // 更新表格
        function updateTable(data) {
            updateDesktopTable(data);
            updateMobileCards(data);
        }

        // 更新桌面端表格
        function updateDesktopTable(data) {
            const tbody = document.getElementById('dataTableBody');

            // 添加淡出动画
            tbody.style.opacity = '0';
            tbody.style.transform = 'translateY(20px)';

            setTimeout(() => {
                tbody.innerHTML = '';

                data.forEach((item, index) => {
                    const row = tbody.insertRow();
                    row.style.opacity = '0';
                    row.style.transform = 'translateY(20px)';
                    row.innerHTML = `
                        <td>${item.phone}</td>
                        <td><span style="color: var(--success-color); font-weight: 600;">${item.balance}元</span></td>
                        <td>${item.total}元</td>
                        <td>${item.vouchers || '<span style="color: var(--text-secondary);">--</span>'}</td>
                        <td>${item.tasks || '<span style="color: var(--text-secondary);">--</span>'}</td>
                        <td style="color: var(--text-secondary);">${item.lastQuery}</td>
                    `;

                    // 添加行动画
                    setTimeout(() => {
                        row.style.transition = 'all 0.3s ease';
                        row.style.opacity = '1';
                        row.style.transform = 'translateY(0)';
                    }, index * 100);
                });

                // 添加淡入动画
                tbody.style.transition = 'all 0.3s ease';
                tbody.style.opacity = '1';
                tbody.style.transform = 'translateY(0)';
            }, 200);
        }

        // 更新移动端卡片
        function updateMobileCards(data) {
            const container = document.getElementById('mobileCardBody');

            // 添加淡出动画
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';

            setTimeout(() => {
                container.innerHTML = '';

                data.forEach((item, index) => {
                    const card = document.createElement('div');
                    card.className = 'mobile-card';
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';

                    card.innerHTML = `
                        <div class="mobile-card-header">
                            <div class="mobile-card-phone">${item.phone}</div>
                            <div class="mobile-card-balance">${item.balance}元</div>
                        </div>
                        <div class="mobile-card-body">
                            <div class="mobile-card-item">
                                <div class="mobile-card-label">历史累计</div>
                                <div class="mobile-card-value">${item.total}元</div>
                            </div>
                            <div class="mobile-card-item">
                                <div class="mobile-card-label">最后查询</div>
                                <div class="mobile-card-value">${item.lastQuery}</div>
                            </div>
                            <div class="mobile-card-item mobile-card-full">
                                <div class="mobile-card-label">话费券</div>
                                <div class="mobile-card-value">${item.vouchers || '--'}</div>
                            </div>
                            <div class="mobile-card-item mobile-card-full">
                                <div class="mobile-card-label">任务状态</div>
                                <div class="mobile-card-value">${item.tasks || '--'}</div>
                            </div>
                        </div>
                    `;

                    container.appendChild(card);

                    // 添加卡片动画
                    setTimeout(() => {
                        card.style.transition = 'all 0.3s ease';
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, index * 100);
                });

                // 添加淡入动画
                container.style.transition = 'all 0.3s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 200);
        }

        // 登录页面动画初始化
        function initLoginAnimations() {
            const loginContainer = document.getElementById('loginContainer');
            const cursorFollower = document.getElementById('cursorFollower');

            if (!loginContainer || !cursorFollower) return;

            // 鼠标跟随效果
            loginContainer.addEventListener('mousemove', function(e) {
                const rect = loginContainer.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                cursorFollower.style.left = x - 10 + 'px';
                cursorFollower.style.top = y - 10 + 'px';
                cursorFollower.style.opacity = '1';
            });

            loginContainer.addEventListener('mouseleave', function() {
                cursorFollower.style.opacity = '0';
            });

            // 浮动元素鼠标交互
            const floatingElements = document.querySelectorAll('.floating-element');
            floatingElements.forEach(element => {
                element.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.2)';
                    this.style.opacity = '1';
                });

                element.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.opacity = '0.7';
                });
            });
        }

        // 退出登录
        function logout() {
            // 清除登录状态和时间戳
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('loginTime');
            window.appData.isLoggedIn = false;

            // 停止自动查询
            stopAutoQuery();

            // 显示登录页面
            document.getElementById('mainApp').style.display = 'none';
            document.getElementById('loginContainer').style.display = 'flex';

            // 清空密码输入框
            document.getElementById('loginPassword').value = '';

            showMessage('已退出登录', 'info');
        }
    </script>
</body>
</html>
