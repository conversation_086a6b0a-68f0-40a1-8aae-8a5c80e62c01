<!DOCTYPE html>
<html>
<head>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <title>喜番 AES 加解密工具</title>
</head>
<body>
<script>
// ==================== 配置 ====================
const AES_KEY = "xifan_kwaix_2024"; // 从 APK 反编译确认

// 生成 16 位随机 IV（字符池来自 AESUtil.RANDOM_CHARSETS）
function generateIV() {
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    let iv = '';
    for (let i = 0; i < 16; i++) {
        iv += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return iv;
}

// AES 加密
function encrypt(plaintext, keyStr, ivStr) {
    const key = CryptoJS.enc.Utf8.parse(keyStr);
    const iv = CryptoJS.enc.Utf8.parse(ivStr);
    const encrypted = CryptoJS.AES.encrypt(plaintext, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    return encrypted.ciphertext.toString(CryptoJS.enc.Base64);
}

// AES 解密
function decrypt(encryptedBase64, keyStr, ivStr) {
    const key = CryptoJS.enc.Utf8.parse(keyStr);
    const iv = CryptoJS.enc.Utf8.parse(ivStr);
    const cipherText = CryptoJS.enc.Base64.parse(encryptedBase64);
    const decrypted = CryptoJS.AES.decrypt(
        { ciphertext: cipherText },
        key,
        { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 }
    );
    const plaintext = decrypted.toString(CryptoJS.enc.Utf8);
    // 尝试格式化为 JSON
    try {
        return JSON.stringify(JSON.parse(plaintext), null, 2);
    } catch (e) {
        return plaintext;
    }
}

// ==================== 快速使用封装 ====================
class XifanCrypto {
    constructor() {
        this.key = AES_KEY;
    }

    // 加密 JSON
    encrypt(json) {
        const iv = generateIV();
        const plaintext = JSON.stringify(json);
        const data = encrypt(plaintext, this.key, iv);
        return { data, iv }; // 返回给服务器的结构
    }

    // 解密响应
    decrypt(data, iv) {
        return decrypt(data, this.key, iv);
    }
}

// ==================== 测试 ====================
const crypto = new XifanCrypto();

// 测试数据
const testJson = {
    "appVersion": "*******",
    "kpn": "XIFAN",
    "channel": "ANDROID_XIAOMI_BA_XFDJXM_NSET_XIFAN_XIAOMI",
    "timestamp": Date.now(),
    "test": "hello"
};

// 执行加密
const { data, iv } = crypto.encrypt(testJson);
console.log("🔒 加密结果:");
console.log("   IV:", iv);
console.log("   Data:", data);

// 执行解密
const decrypted = crypto.decrypt(data, iv);
console.log("🔓 解密结果:");
console.log(decrypted);

// 验证
console.log("✅ 加密解密一致:", JSON.stringify(testJson) === JSON.parse(decrypted));
</script>
</body>
</html>