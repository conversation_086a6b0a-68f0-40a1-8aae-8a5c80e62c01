#!/bin/bash

# 联通查询系统部署验证脚本
# 验证所有功能是否正常工作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

echo "========================================"
echo "联通查询系统 - 部署验证脚本"
echo "========================================"
echo

# 等待服务启动
log_step "等待服务启动..."
sleep 10

# 检查服务是否运行
log_step "检查服务状态..."
if curl -f http://localhost:2032/api/health > /dev/null 2>&1; then
    log_info "✅ 服务运行正常"
else
    log_error "❌ 服务未运行或健康检查失败"
    exit 1
fi

# 检查主页是否可访问
log_step "检查主页访问..."
if curl -f http://localhost:2032/ > /dev/null 2>&1; then
    log_info "✅ 主页可正常访问"
else
    log_error "❌ 主页访问失败"
    exit 1
fi

# 检查favicon是否可访问
log_step "检查网站图标..."
if curl -f http://localhost:2032/favicon.svg > /dev/null 2>&1; then
    log_info "✅ 网站图标加载正常"
else
    log_warn "⚠️ 网站图标加载失败"
fi

# 检查注册状态API
log_step "检查注册状态API..."
if curl -f http://localhost:2032/api/check-registration > /dev/null 2>&1; then
    log_info "✅ 注册状态API正常"
else
    log_error "❌ 注册状态API失败"
    exit 1
fi

# 检查自动查询状态API
log_step "检查自动查询状态API..."
if curl -f http://localhost:2032/api/get_auto_query_status > /dev/null 2>&1; then
    log_info "✅ 自动查询状态API正常"
else
    log_error "❌ 自动查询状态API失败"
    exit 1
fi

# 检查数据API
log_step "检查数据API..."
if curl -f http://localhost:2032/api/data > /dev/null 2>&1; then
    log_info "✅ 数据API正常"
else
    log_error "❌ 数据API失败"
    exit 1
fi

echo
log_info "========================================"
log_info "🎉 部署验证完成！所有核心功能正常"
log_info "========================================"
echo
log_info "🌐 访问地址: http://localhost:2032"
log_info "🔧 管理功能:"
log_info "   - 严格的单次注册限制"
log_info "   - 后端独立自动查询"
log_info "   - 专业的网站图标"
log_info "   - 完整的API接口"
echo
log_info "✨ 系统已准备就绪，可以开始使用！"
