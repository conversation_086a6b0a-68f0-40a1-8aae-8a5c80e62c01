# 泛微表单翻页优化 - 超精简版说明

## 核心方法说明

### 1. 事件委托（Event Delegation）
```javascript
['input', 'change'].forEach(type => 
    document.addEventListener(type, (e) => {
        const field = e.target.closest('[id="zjfd"], [id="yin1"]');
        field && setColor(field);
    }, true)
);
```
- 在 document 级别监听事件，自动处理所有动态添加的元素
- 使用 `closest()` 查找目标元素
- 第三个参数 `true` 表示在捕获阶段处理事件

### 2. MutationObserver
```javascript
new MutationObserver(() => init()).observe(document.body, { 
    childList: true, 
    subtree: true 
});
```
- 监听整个 document.body 的 DOM 变化
- 任何子元素变化都会触发重新初始化
- 自动处理翻页、动态加载等场景

### 3. WeFormSDK 事件绑定
```javascript
sdk.bindFieldChangeEvent(mark, (data) => 
    document.querySelectorAll(`[fieldid^="${data.id.slice(5)}"]`).forEach(setColor)
);
```
- 使用泛微官方 SDK 绑定字段变化事件
- 通过 `convertFieldNameToId` 获取正确的字段标识
- 使用全局标记 `window._boundTables` 避免重复绑定

## 配置说明

```javascript
const CONFIG = {
    tables: ['mxb1', 'mxb2'],  // 明细表名称
    fields: ['zjfd', 'yin1'],   // 需要监控的字段
    colorField: 'zjfd'          // 用于WeFormSDK绑定的字段
};
```

## 在其他代码中复用

1. 复制整个自执行函数
2. 修改 CONFIG 对象中的配置：
   - `tables`: 改为你的明细表名称
   - `fields`: 改为需要监控的字段ID
   - `colorField`: 改为需要绑定事件的字段名

## 代码特点

- **50行以内**：精简到极致
- **自动处理翻页**：无需额外配置
- **性能优化**：使用事件委托，避免重复绑定
- **易于复用**：配置化设计，方便移植到其他项目