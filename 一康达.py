"""
康师傅扫码签到

支持多用户运行

入口：微信小程序---康师傅

多用户用&或者@隔开
例如：
export ksfenv=api:port#phone#wxid1&wxid2#lat#lng#actid

cron: 55 12 * * *
const $ = new Env("ksfenv");
"""
import requests
import re
import os
import time
import json
import base64
from datetime import datetime
from bs4 import BeautifulSoup
import cloudscraper
import io
import logging
from urllib.parse import urlparse, parse_qs, unquote
import random
#from notify import send
timesleep = random.randint(1, 5)

# 创建 StringIO 对象
log_stream = io.StringIO()

# 配置 logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# 创建控制台 Handler
console_handler = logging.StreamHandler()
console_handler.setFormatter(
    logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

# 创建 StringIO Handler
stream_handler = logging.StreamHandler(log_stream)
# stream_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

# 将两个 Handler 添加到 logger
logger.addHandler(console_handler)
logger.addHandler(stream_handler)

#分割变量
API_URL = os.environ.get("yuanshen_api")
#UUID = os.environ.get("syjsyuuid")
STATE = "d8d1cfed555911f085f45254007c69a1"

ids = [3, 10, 26, 210, 247, 273, 274, 275, 394, 395, 711, 773, 871, 1122, 1309, 1330, 1331, 1332, 1333, 1334, 1335, 1336, 1337, 1338, 1339, 1340]

def send_request(
        url,
        method="GET",
        headers=None,
        params=None,
        data=None,
        json_data=None,
        retry=3,
        retry_delay=2,
        timeout=10,
        cloudscrape=False
    ):
    """
    发送 HTTP 请求的通用函数
    
    参数:
        url (str): 请求的 URL
        method (str): HTTP 方法 (GET 或 POST)，默认为 GET
        headers (dict): 请求头
        params (dict): URL 查询参数
        data (dict): 表单数据 (application/x-www-form-urlencoded)
        json_data (dict): JSON 数据 (application/json)
        retry (int): 重试次数，默认为 3
        retry_delay (int): 重试延迟时间（秒），默认为 2
        timeout (int): 请求超时时间（秒），默认为 10
        cloudscrape (bool): 是否使用 cloudscraper 绕过 Cloudflare 防护
    
    返回:
        requests.Response: 响应对象
        None: 如果请求失败
    """
    # 创建请求会话
    session = cloudscraper.create_scraper() if cloudscrape else requests.Session()
    
    # 设置默认 User-Agent
    headers = headers or {}
    if "User-Agent" not in headers:
        headers["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    
    attempt = 0
    while attempt <= retry:
        try:
            if method.upper() == "GET":
                response = session.get(
                    url,
                    headers=headers,
                    params=params,
                    timeout=timeout
                )
            elif method.upper() == "POST":
                if json_data:
                    response = session.post(
                        url,
                        headers=headers,
                        json=json_data,
                        timeout=timeout
                    )
                else:
                    response = session.post(
                        url,
                        headers=headers,
                        data=data,
                        params=params,
                        timeout=timeout
                    )
            else:
                raise ValueError(f"不支持的 HTTP 方法: {method}")
            
            # 检查状态码
            response.raise_for_status()
            return response
        
        except (requests.exceptions.RequestException, ValueError) as e:
            attempt += 1
            logger.warning(f"请求失败 ({attempt}/{retry}): {e}")
            if attempt <= retry:
                time.sleep(retry_delay)
    
    logger.error(f"请求 {url} 失败，重试 {retry} 次后仍不成功")
    return None


def get_wxid(api_url):
    """
    获取当前已登录的微信账号
    
    返回:
        list: 当前已登录的wxid
    """
    # 准备请求参数
    url = "http://" + api_url + "/getallwx"
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    try:
        # 发送请求
        response = send_request(url, method="GET", headers=headers)
        data = response.json()
        wx_info = [{"Wxid": item['Wxid'], "wxname": item['wxname']} for item in data]
        if wx_info:
            logger.info(f"总共获取到{len(wx_info)}个用户")
            return wx_info
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return []
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        return []

def get_redirect_url(wxid):
    url = "http://okok.yzre.cn/v1/toc/mp/get_redirect_url"
    payload = {
        "url": "http://okok.yzre.cn/pages/mp/callback",
        "state": 99
    }
    headers = {
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090c33) XWEB/14185 Flue",
      'Accept-Encoding': "gzip, deflate",
        'Content-Type': "application/json",
        'x-aid': "99",
        'Content-Type': "application/json; charset=UTF-8",
        'Origin': "http://okok.yzre.cn",
        'Accept-Language': "zh-CN,zh;q=0.9"
    }
    response = send_request(url, method="POST", json_data = payload, headers=headers)

    data = response.json()

    if data["code"] == 200:
        logger.info(f"redirect_url: {data['data']}")
        redirect_url = data["data"]
        return redirect_url
    else:
        logger.error(f"{data['msg']}")
        return False

def extract_uri(url: str, params: str) -> str:
    """
    从URL中提取并解码redirect_uri参数
    
    参数:
        url: 包含redirect_uri参数的完整URL字符串
        
    返回:
        解码后的redirect_uri值，如果未找到则返回None
    """
    try:
        # 解析URL结构
        parsed_url = urlparse(url)
        # 解析查询参数
        query_params = parse_qs(parsed_url.query)
        
        # 获取并解码redirect_uri
        if params in query_params:
            encoded_uri = query_params[params][0]  # 提取第一个值
            return unquote(encoded_uri)  # URL解码
        
        return None  # 未找到redirect_uri参数
    
    except Exception as e:
        print(f"解析URL时出错: {e}")
        return None

def getCodeUrl(wxid, redirect_url):
    url = "http://" + API_URL.split(":")[0] + ":8057/api/OfficialAccounts/OauthAuthorize"
    redirectUrl = extract_uri(redirect_url, "redirect_uri")
    payload = {
        "Wxid": wxid,
        "Appid": "wxe652a4f66d849cce",
        "Url": redirectUrl
    }
    headers = {
        'Content-Type': "application/json"
    }
    response = send_request(url, method="POST", json_data = payload, headers=headers)
    data = response.json()
    if data["Code"] == 0:
        redirectUrl = data["Data"]["redirectUrl"]
        logger.info(f"CodeUrl: {redirectUrl}")
        return redirectUrl
    else:
        logger.error(f"{data['Message']}")
        return False

def getToken(redirectUrl):
    try:
        url = "http://okok.yzre.cn/v1/toc/mp/callback"
        code = extract_uri(redirectUrl, "code")
        payload = {
            "code": code,
            "state": 99
        }

        headers = {
            'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090c33) XWEB/14185 Flue",
            'Accept-Encoding': "gzip, deflate",
            'Content-Type': "application/json",
            'x-aid': "99",
            'Content-Type': "application/json; charset=UTF-8",
            'Origin': "http://okok.yzre.cn",
            'Referer': redirectUrl,
            'Accept-Language': "zh-CN,zh;q=0.9"
        }
        response = send_request(url, method="POST", json_data = payload, headers=headers)

        data = response.json()

        if data["code"] == 200:
            userInfo = data["data"]["user_info"]
            token = data["data"]["token"]
            logger.info(data["msg"])
            return token, userInfo
        else:
            logger.error(data["msg"])
            return False
    except Exception as e:
        logger.error(e)

def get_detail(id, token):
    url = "http://okok.yzre.cn/v1/toc/activity/detail"
    payload = {
        "id": int(id)
    }
    headers = {
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090c33) XWEB/14185 Flue",
        'Accept-Encoding': "gzip, deflate",
        'Content-Type': "application/json",
        'x-aid': "99",
        'x-token': token,
        'Content-Type': "application/json; charset=UTF-8",
        'Origin': "http://okok.yzre.cn",
        'Referer': "http://okok.yzre.cn/?aid=99&vid=395",
        'Accept-Language': "zh-CN,zh;q=0.9"
    }

    response = send_request(url, method="POST", json_data = payload, headers=headers)

    data = response.json()

    if data["code"] == 200:
        activityId = data["data"]["activity"]["id"]
        logger.info(f"当前活动ID: {id} | 活动存在，开始做活动")
        return activityId
    else:
        logger.info(f"当前活动ID: {id} | {data['msg']}")
        return False

def getanswer(token, activityId):
    url = "http://okok.yzre.cn/v1/toc/question/detail"
    payload = {
        "activity_id": int(activityId)
    }
    headers = {
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090c33) XWEB/14185 Flue",
        'Accept-Encoding': "gzip, deflate",
        'Content-Type': "application/json",
        'x-aid': "99",
        'x-token': token,
        'Content-Type': "application/json; charset=UTF-8",
        'Origin': "http://okok.yzre.cn",
        'Accept-Language': "zh-CN,zh;q=0.9"
    }
    response = send_request(url, method="POST", json_data = payload, headers=headers)
    data = response.json()
    if data["code"] == 200:
        questions = []
        for question in data["data"]["questionList"]:
            questio = {}
            questio["id"] = question["question_id"]
            questio["answer"] = question["answer"]
            questions.append(questio)
        return questions
    else:
        logger.error(data["msg"])
        return False

def videotime(token, activityId):
    url = "http://okok.yzre.cn/v1/toc/activity/submit"
    payload = {
        "id": int(activityId),
        "view_duration": random.randint(500000, 1000000),
        "is_finished": 1
    }
    headers = {
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090c33) XWEB/14185 Flue",
        'Accept-Encoding': "gzip, deflate",
        'Content-Type': "application/json",
        'x-aid': "99",
        'x-token': token,
        'Content-Type': "application/json; charset=UTF-8",
        'Origin': "http://okok.yzre.cn",
        'Accept-Language': "zh-CN,zh;q=0.9"
    }
    response = send_request(url, method="POST", json_data = payload, headers=headers)
    data = response.json()
    if data["code"] == 200:
        logger.info(f"{data['msg']}")
    else:
        logger.error(f"{data['msg']}")

def answer(token, activityId, questions):
    url = "http://okok.yzre.cn/v1/toc/question/submit"
    payload = {
        "view_duration": 161679,
        "activity_id": int(activityId),
        "id": questions[0]["id"],
        "user_answer": questions[0]["answer"]
    }
    headers = {
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090c33) XWEB/14185 Flue",
        'Accept-Encoding': "gzip, deflate",
        'Content-Type': "application/json",
        'x-aid': "99",
        'x-token': token,
        'Content-Type': "application/json; charset=UTF-8",
        'Origin': "http://okok.yzre.cn",
        'Accept-Language': "zh-CN,zh;q=0.9"
    }

    response = send_request(url, method="POST", json_data = payload, headers=headers)

    data = response.json()
    
    if data["code"] == 200:
        logger.info(f"{data['msg']}")
    else:
        logger.error(f"{data['msg']}")

def main():
    #updateProgress("cf156360-ad22-4431-95fe-9c3334144cc9", 31701)
    wx_info = get_wxid(API_URL)
    wx_for = wx_info[0]
    logger.info(f"当前执行用户: {wx_for['wxname']}")
    redirect_url = get_redirect_url(wx_for["Wxid"])
    #time.sleep(random.randint(1, 5))
    CodeUrl = getCodeUrl(wx_for["Wxid"], redirect_url)
    #time.sleep(random.randint(1, 5))
    Token, user_info = getToken(CodeUrl)
    logger.info(f"当前用户ID: {user_info['id']}   当前用户名: {user_info['name']}")
    #time.sleep(random.randint(1, 5))
    #for id in ids:
    for id in range(1341, 1352):
        activityId = get_detail(id, Token)
        #time.sleep(random.randint(1, 5))
        if activityId:
            for wx in wx_info:
                logger.info(f"当前执行用户: {wx['wxname']}")
                redirect_url = get_redirect_url(wx["Wxid"])
                time.sleep(random.randint(1, 5))
                CodeUrl = getCodeUrl(wx["Wxid"], redirect_url)
                time.sleep(random.randint(1, 5))
                Token, user_info = getToken(CodeUrl)
                logger.info(f"当前用户ID: {user_info['id']}   当前用户名: {user_info['name']}")
                time.sleep(random.randint(1, 5))
                activityid = get_detail(id, Token)
                time.sleep(random.randint(1, 5))
                questions = getanswer(Token, activityid)
                time.sleep(random.randint(1, 5))
                videotime(Token, activityid)
                time.sleep(random.randint(1, 5))
                answer(Token, activityid, questions)
                time.sleep(random.randint(1, 5))
        id = id + 1


if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        logger.error(e)
    try:
        log_contents = log_stream.getvalue()
    except Exception as e:
        logger.info('小错误')