import hashlib
import time

def crack_signature_algorithm():
    """基于token过期错误，我们知道格式正确，现在破解签名算法"""
    
    # 已知有效的参数组合
    consumer = "188880000002"
    timestamp = "1752637422784"  # 已知有效时间戳
    nonce = "9c7b473eef2946b4839f4e5361250699"  # 已知有效nonce
    tenant_id = "100098706"
    open_id = "oRuFi5Afjw0Nn69fObkQ0AWJxaZk"
    expected_sign = "f1dce0b8cdbd52c6cb828ee96e4088c3"
    
    print(f"目标签名: {expected_sign}")
    print("基于'token过期'错误，我们知道格式正确，专注于签名算法")
    print("=" * 60)
    
    # 新的尝试：也许签名算法包含了一些我们遗漏的参数
    
    # 尝试1: 包含完整的token字符串（除了sign本身）
    def test_full_token_string():
        # 完整token: consumer=188880000002&timestamp=1752637422784&nonce=9c7b473eef2946b4839f4e5361250699&sign=f1dce0b8cdbd52c6cb828ee96e4088c3&tenantId=100098706&cid=&openId=oRuFi5Afjw0Nn69fObkQ0AWJxaZk&v=20220613
        # 去掉sign部分
        token_without_sign = f"consumer={consumer}&timestamp={timestamp}&nonce={nonce}&tenantId={tenant_id}&cid=&openId={open_id}&v=20220613"
        sign = hashlib.md5(token_without_sign.encode()).hexdigest()
        print(f"完整token(无sign): {token_without_sign}")
        print(f"结果: {sign}")
        return sign == expected_sign
    
    # 尝试2: 按token中的实际顺序，但不包含cid和v
    def test_token_order_no_extra():
        token_string = f"consumer={consumer}&timestamp={timestamp}&nonce={nonce}&tenantId={tenant_id}&openId={open_id}"
        sign = hashlib.md5(token_string.encode()).hexdigest()
        print(f"token顺序(无额外): {token_string}")
        print(f"结果: {sign}")
        return sign == expected_sign
    
    # 尝试3: 也许需要包含一些特殊的分隔符或前缀
    def test_with_prefixes():
        base_string = f"consumer={consumer}&timestamp={timestamp}&nonce={nonce}&tenantId={tenant_id}&openId={open_id}"
        
        prefixes = [
            "sign:",
            "token:",
            "auth:",
            "key:",
            "",
            "rtmap:",
            "api:",
        ]
        
        for prefix in prefixes:
            test_string = prefix + base_string
            sign = hashlib.md5(test_string.encode()).hexdigest()
            print(f"前缀({prefix}): {sign}")
            if sign == expected_sign:
                return prefix
        return False
    
    # 尝试4: 也许签名算法使用了不同的编码
    def test_different_encodings():
        base_string = f"consumer={consumer}&timestamp={timestamp}&nonce={nonce}&tenantId={tenant_id}&openId={open_id}"
        
        # UTF-8 (默认)
        sign = hashlib.md5(base_string.encode('utf-8')).hexdigest()
        print(f"UTF-8编码: {sign}")
        if sign == expected_sign:
            return "utf-8"
            
        # GBK编码
        try:
            sign = hashlib.md5(base_string.encode('gbk')).hexdigest()
            print(f"GBK编码: {sign}")
            if sign == expected_sign:
                return "gbk"
        except:
            pass
            
        # ASCII编码
        try:
            sign = hashlib.md5(base_string.encode('ascii')).hexdigest()
            print(f"ASCII编码: {sign}")
            if sign == expected_sign:
                return "ascii"
        except:
            pass
        
        return False
    
    # 尝试5: 也许需要对参数值进行某种处理
    def test_parameter_processing():
        # 尝试对openId进行URL解码（虽然看起来不需要）
        # 尝试对某些参数进行特殊处理
        
        # 也许timestamp需要特殊处理
        timestamp_variants = [
            timestamp,  # 原始
            timestamp[:-3],  # 去掉毫秒
            str(int(timestamp) // 1000),  # 转换为秒
        ]
        
        for ts in timestamp_variants:
            test_string = f"consumer={consumer}&timestamp={ts}&nonce={nonce}&tenantId={tenant_id}&openId={open_id}"
            sign = hashlib.md5(test_string.encode()).hexdigest()
            print(f"时间戳变体({ts}): {sign}")
            if sign == expected_sign:
                return ts
        
        return False
    
    # 尝试6: 也许签名字符串需要特殊的排序
    def test_special_sorting():
        # 尝试不同的参数排序方式
        params = {
            'consumer': consumer,
            'timestamp': timestamp,
            'nonce': nonce,
            'tenantId': tenant_id,
            'openId': open_id
        }
        
        # 按值排序
        sorted_by_value = sorted(params.items(), key=lambda x: x[1])
        test_string = '&'.join([f"{k}={v}" for k, v in sorted_by_value])
        sign = hashlib.md5(test_string.encode()).hexdigest()
        print(f"按值排序: {test_string}")
        print(f"结果: {sign}")
        if sign == expected_sign:
            return "sort_by_value"
        
        # 按键长度排序
        sorted_by_key_length = sorted(params.items(), key=lambda x: len(x[0]))
        test_string = '&'.join([f"{k}={v}" for k, v in sorted_by_key_length])
        sign = hashlib.md5(test_string.encode()).hexdigest()
        print(f"按键长度排序: {test_string}")
        print(f"结果: {sign}")
        if sign == expected_sign:
            return "sort_by_key_length"
        
        return False
    
    # 执行所有测试
    tests = [
        ("完整token字符串", test_full_token_string),
        ("token顺序无额外参数", test_token_order_no_extra),
        ("前缀测试", test_with_prefixes),
        ("编码测试", test_different_encodings),
        ("参数处理测试", test_parameter_processing),
        ("特殊排序测试", test_special_sorting),
    ]
    
    for test_name, test_func in tests:
        print(f"\n=== {test_name} ===")
        try:
            result = test_func()
            if result and result != False:
                print(f"✅ 找到正确方法: {test_name} - {result}")
                return test_name, result
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n❌ 仍未找到正确的签名算法")
    return None, None

if __name__ == "__main__":
    print("=== 最终签名算法破解 ===")
    method, detail = crack_signature_algorithm()
    if method:
        print(f"\n🎉 成功破解签名算法!")
        print(f"方法: {method}")
        print(f"详情: {detail}")
    else:
        print("\n😞 需要更深入的分析")
