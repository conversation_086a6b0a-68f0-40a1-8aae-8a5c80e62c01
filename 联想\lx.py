#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
File: lenovo_sign.py(联想智选签到)
Author: marigold233
Date: 2023/02/25 23:00
cron: 0 12 * * * lenovo_sign.py
new Env('联想智选签到');

青龙面板使用
安装脚本依赖requests toml
复制config.toml到脚本管理对应目录下
在config.toml里面填写账号和推送
"""

import base64
import json
import logging
import os
import random
import re
import smtplib
from email.header import Header
from email.mime.text import MIMEText
from email.utils import formataddr
from smtplib import SMTP_SSL
from sys import exit
from time import sleep

import requests
import toml
from requests.utils import cookiejar_from_dict, dict_from_cookiejar

USER_AGENT = [
    "Mozilla/5.0 (Linux; Android 14.0; Xiaomi 13 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.5993.117 Mobile Safari/537.36",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
]


class Push_messages:
    class Server_chan:
        def __init__(self, send_key: str) -> None:
            self.send_key = send_key

        def send_message(self, content: str) -> bool:
            data = {"title": "联想签到", "desp": content}
            response = requests.post(
                f"https://sctapi.ftqq.com/{self.send_key}.send", data=data
            )
            res_data = response.json().get("data")
            pushid = res_data.get("pushid")
            readkey = res_data.get("readkey")
            result = requests.get(
                f"https://sctapi.ftqq.com/push?id={pushid}&readkey={readkey}"
            )
            return True if result.json().get("code") == 0 else False

    class Wechat_message:
        def __init__(self, corpid: str, corpsecret: str, agentid: str) -> None:
            self.corpid = corpid
            self.corpsecret = corpsecret
            self.agentid = agentid
            self.token = (
                requests.get(
                    f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={self.corpid}&corpsecret={self.corpsecret}"
                )
                .json()
                .get("access_token")
            )

        def send_message(self, content: str) -> bool:
            data = {
                "touser": "@all",
                "msgtype": "text",
                "agentid": self.agentid,
                "text": {"content": content},
                "safe": 0,
            }
            response = requests.post(
                f"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={self.token}",
                data=json.dumps(data),
            )
            return True if response.json().get("errcode") == 0 else False

    class Dingtalk_message:
        def __init__(self, ding_accesstoken: str) -> None:
            self.ding_accesstoken = ding_accesstoken

        def send_message(self, content: str) -> bool:
            data = {
                "msgtype": "text",
                "text": {"content": content},
                "at": {"isAtAll": True},
            }
            response = requests.post(
                f"https://oapi.dingtalk.com/robot/send?access_token={self.ding_accesstoken}",
                data=json.dumps(data),
            )
            return True if response.json().get("errcode") == 0 else False

    class Email_message:
        def __init__(self, sender_email: str, sender_password: str, receiver_email: str, smtp_server: str,
                     smtp_port: int) -> None:
            self.sender_email = sender_email
            self.sender_password = sender_password
            self.receiver_email = receiver_email
            self.smtp_server = smtp_server
            self.smtp_port = smtp_port

        def send_message(self, content: str) -> bool:
            receiver_email = [self.receiver_email]

            message = MIMEText(content, 'plain', 'utf-8')
            message['Subject'] = Header("联想智选定时签到结果", "utf-8")
            message['From'] = Header("联想智选定时签到程序", "utf-8")
            message['To'] = receiver_email[0]

            try:
                smtp = SMTP_SSL(self.smtp_server, self.smtp_port)
                smtp.login(self.sender_email, self.sender_password)
                smtp.sendmail(
                    self.sender_email, receiver_email, message.as_string())
                smtp.quit()
                return True
            except smtplib.SMTPException as e:
                print('send email error', e)
                return False

    class QQEmail_message:
        def __init__(self, sender_email: str, sender_password: str, receiver_email: str, smtp_server: str,
                     smtp_port: int) -> None:
            self.sender_email = sender_email
            self.sender_password = sender_password
            self.receiver_email = receiver_email
            self.smtp_server = smtp_server
            self.smtp_port = smtp_port

        def send_message(self, content: str) -> bool:
            tmp = self.receiver_email.split(",")
            receiver_email = tmp
            subject = "联想智选定时签到结果"
            from_head = "联想智选定时签到程序"
            message = MIMEText(content, 'plain', 'utf-8')

            # ✅ 使用 formataddr + Header 自动处理编码
            message['From'] = formataddr((str(Header(from_head, 'utf-8')), self.sender_email))
            message['Subject'] = Header(subject, 'utf-8')

            try:
                smtp = SMTP_SSL(self.smtp_server, self.smtp_port)
                smtp.login(self.sender_email, self.sender_password)
                for i in range(len(receiver_email)):
                    message['To'] = formataddr((str(Header(receiver_email[i], 'utf-8')), self.receiver_email))
                    smtp.sendmail(
                        self.sender_email, receiver_email[i], message.as_string())
                smtp.quit()
                return True
            except smtplib.SMTPException as e:
                print('send qq email error', e)
                return False


def set_push_type():
    for type, key in config.get("message_push").items():
        key_list = key.values()
        if "".join(key_list):
            return getattr(Push_messages(), type)(*key_list).send_message
    else:
        return logger


def login(username, password):
    def get_cookie():
        session.headers = {
            "user-agent": ua,
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        }
        session.get(url="https://reg.lenovo.com.cn/auth/rebuildleid")
        session.get(
            url="https://reg.lenovo.com.cn/auth/v1/login?ticket=5e9b6d3d-4500-47fc-b32b-f2b4a1230fd3&ru=https%3A%2F%2Fmclub.lenovo.com.cn%2F"
        )
        data = f"account={username}&password={base64.b64encode(str(password).encode()).decode()}\
            &ps=1&ticket=5e9b6d3d-4500-47fc-b32b-f2b4a1230fd3&codeid=&code=&slide=v2&applicationPlatform=2&shopId=\
                1&os=web&deviceId=BIT%2F8ZTwWmvKpMsz3bQspIZRY9o9hK1Ce3zKIt5js7WSUgGQNnwvYmjcRjVHvJbQ00fe3T2wxgjZAVSd\
                    OYl8rrQ%3D%3D&t=*************&websiteCode=********&websiteName=%25E5%2595%2586%25E5%259F%258E%25E\
                        7%25AB%2599&forwardPageUrl=https%253A%252F%252Fmclub.lenovo.com.cn%252F"
        login_response = session.post(
            url="https://reg.lenovo.com.cn/auth/v2/doLogin", data=data
        )
        if login_response.json().get("ret") == "1":
            logger(f"{username}账号或密码错误")
            return None
        ck_dict = dict_from_cookiejar(session.cookies)
        config["cookies"][username] = f"{ck_dict}"
        toml.dump(config, open(config_file, "w"))
        session.cookies = cookiejar_from_dict(ck_dict)
        return session

    session = requests.Session()
    session.headers = {
        "user-agent": ua,
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
    }
    if cookie_dict := config.get("cookies").get(username):
        session.cookies = cookiejar_from_dict(eval(cookie_dict))
        ledou = session.post(
            "https://i.lenovo.com.cn/info/uledou.jhtml",
            data={"sts": "b044d754-bda2-4f56-9fea-dcf3aecfe782"},
        )
        try:
            int(ledou.text)
        except ValueError:
            logger(f"{username} ck有错，重新获取ck并保存")
            session = get_cookie()
            return session
        logger(f"{username} ck没有错")
        return session
    else:
        logger(f"{username} ck为空，重新获取ck并保存")
        session = get_cookie()
        return session


def sign(session):
    res = session.get(url="https://mclub.lenovo.com.cn/signlist/")
    token = re.findall('token\s=\s"(.*?)"', res.text)[0]
    data = f"_token={token}&memberSource=1"
    headers = {
        "Host": "mclub.lenovo.com.cn",
        "pragma": "no-cache",
        "cache-control": "no-cache",
        "accept": "application/json, text/javascript, */*; q=0.01",
        "origin": "https://mclub.lenovo.com.cn",
        "x-requested-with": "XMLHttpRequest",
        "user-agent": ua
                      + "/lenovoofficialapp/16554342219868859_10128085590/newversion/versioncode-1000080/",
        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
        "referer": "https://mclub.lenovo.com.cn/signlist?pmf_group=in-push&pmf_medium=app&pmf_source=Z00025783T000",
        "accept-language": "zh-CN,en-US;q=0.8",
    }
    sign_response = session.post(
        "https://mclub.lenovo.com.cn/signadd", data=data, headers=headers
    )
    try:
        sign_cal_response = session.get(url="https://mclub.lenovo.com.cn/getsignincal")
        sign_cal_data = sign_cal_response.json()
        # 优先使用 continueCount，其次使用 continuity_day
        signin_cal = sign_cal_data.get("signinCal", {})
        sign_days = signin_cal.get("continueCount")
        if sign_days is None:
            sign_days = sign_cal_data.get("continuity_day")
        if sign_days is None:
            sign_days = signin_cal.get("continuity_day")
        if sign_days is None:
            sign_days = 0
        if sign_days is None:
            sign_days = 0

        # 同时从签到API获取延保信息作为备用
        backup_service_amount = signin_cal.get("yanbaoValue", 0)

# logger(f"{username} 签到天数: {sign_days}")  # 调试用
    except Exception as e:
        logger(f"{username} 获取签到天数失败: {e}")
        sign_days = 0
    sign_user_info = session.get("https://mclub.lenovo.com.cn/signuserinfo")
    try:
        user_info_data = sign_user_info.json()

        # 检查是否需要重新登录
        if user_info_data.get("res") == "Must login":
            # logger(f"{username} 需要重新登录，尝试获取基本信息")  # 调试用
            # 直接使用备用接口获取乐豆
            ledou_response = session.post(
                "https://i.lenovo.com.cn/info/uledou.jhtml",
                data={"sts": "b044d754-bda2-4f56-9fea-dcf3aecfe782"},
            )
            try:
                ledou = int(ledou_response.text)
            except (ValueError, TypeError):
                ledou = "未知"
            # 使用从签到API获取的延保信息
            serviceAmount = backup_service_amount if backup_service_amount > 0 else "未知"
        else:
            # 正常获取信息
            serviceAmount = user_info_data.get("serviceAmount")
            ledou = user_info_data.get("ledou")

            # 处理乐豆信息
            if ledou is None or ledou == "":
                ledou_response = session.post(
                    "https://i.lenovo.com.cn/info/uledou.jhtml",
                    data={"sts": "b044d754-bda2-4f56-9fea-dcf3aecfe782"},
                )
                try:
                    ledou = int(ledou_response.text)
                except (ValueError, TypeError):
                    ledou = "未知"
            else:
                # 如果ledou是字符串，转换为整数
                try:
                    ledou = int(ledou)
                except (ValueError, TypeError):
                    pass

            # 处理延保信息
            if serviceAmount is None:
                # 使用从签到API获取的延保信息作为备用
                serviceAmount = backup_service_amount if backup_service_amount > 0 else "未知"

# logger(f"{username} 最终信息 - 乐豆: {ledou}, 延保: {serviceAmount}")  # 调试用

    except Exception as e:
        logger(f"{username} 获取用户信息异常: {e}")
        # 尝试从备用接口获取乐豆
        try:
            ledou_response = session.post(
                "https://i.lenovo.com.cn/info/uledou.jhtml",
                data={"sts": "b044d754-bda2-4f56-9fea-dcf3aecfe782"},
            )
            ledou = int(ledou_response.text)
        except:
            ledou = "未知"
        serviceAmount = "未知"
    session.close()

    # 格式化显示信息
    ledou_text = f"{ledou}个" if ledou != "未知" else "未知"
    service_text = f"{serviceAmount}天" if serviceAmount != "未知" else "无"

    if sign_response.json().get("success"):
        return f"\U00002705账号{username}签到成功, \U0001F4C6连续签到{sign_days}天, \U0001F954共有乐豆{ledou_text}, \U0001F4C5延保服务{service_text}\n"
    else:
        return f"\U0001F6AB账号{username}今天已经签到, \U0001F4C6连续签到{sign_days}天, \U0001F954共有乐豆{ledou_text}, \U0001F4C5延保服务{service_text}\n"


def main():
    global logger, config_file, config, ua, username
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s: %(message)s"
    )
    logger = logging.getLogger(__name__).info
    # config_file = r"../private-config/config.toml"
    # config = toml.load(config_file)
    # 读取CI指定的配置
    config_file = os.getenv("CONFIG_FILE", "config.toml")
    config = toml.load(config_file)
    account = config.get("account")
    if not account:
        exit(1)
    if not (ua := config.get("browser").get("ua")):
        ua = random.choice(USER_AGENT)
        config["browser"]["ua"] = ua
    push = set_push_type()
    message = "联想签到: \n"
    for username, password in account.items():
        session = login(username, password)
        if not session:
            continue
        message += sign(session)
        # sleep(random.randint(0,20))
    push(message)


if __name__ == "__main__":
    # sleep(random.randint(0,10)) # 延时 0-10s (原来是0-120s，现在改为更短的延时)
    main()