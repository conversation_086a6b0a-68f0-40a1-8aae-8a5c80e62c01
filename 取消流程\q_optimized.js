// 泛微表单翻页优化版本 - 解决翻页后事件失效问题

// 工具函数：解析数字
function parseNumber(value) {
    if (!value) return null;
    const matches = String(value).match(/[+-]?([0-9,]*\.?[0-9]+)/);
    return matches ? Number(matches[0].replace(/,/g, '')) : null;
}

// 工具函数：设置字段颜色
function setFieldColor(field) {
    const input = field.querySelector("input, span") || field;
    const numValue = parseNumber(input.textContent || input.value);
    field.style.backgroundColor = (numValue > 0) ? "red" : "";
}

// 全局变量存储已绑定的字段，避免重复绑定
const boundFields = new Set();

// 初始化函数：处理所有字段的颜色设置和事件绑定
function initializeFields() {
    console.log('[泛微表单] 开始初始化字段...');
    
    // 1. 处理zjfd和yin1字段的颜色
    ['zjfd', 'yin1'].forEach(id => {
        document.querySelectorAll(`[id="${id}"]`).forEach(field => {
            setFieldColor(field);
            console.log(`[泛微表单] 设置字段颜色: ${id}`);
        });
    });
    
    // 2. 重新绑定WeFormSDK事件（如果需要）
    if (window.WeFormSDK) {
        const weFormSdk = window.WeFormSDK.getWeFormInstance();
        ['mxb1', 'mxb2'].forEach(table => {
            const fieldMark = weFormSdk.convertFieldNameToId('zjfd', weFormSdk.convertFieldNameToId(table));
            
            // 检查是否已绑定，避免重复
            if (!boundFields.has(fieldMark)) {
                weFormSdk.bindFieldChangeEvent(fieldMark, (data) => {
                    document.querySelectorAll(`[fieldid^="${data.id.slice(5)}"]`).forEach(setFieldColor);
                });
                boundFields.add(fieldMark);
                console.log(`[泛微表单] 绑定字段变化事件: ${fieldMark}`);
            }
        });
    }
}

// 使用事件委托处理动态内容
function setupEventDelegation() {
    // 在document级别使用事件委托，确保捕获所有动态元素
    document.addEventListener('input', function(e) {
        const field = e.target.closest('[id="zjfd"], [id="yin1"]');
        if (field) {
            setFieldColor(field);
        }
    }, true);
    
    document.addEventListener('change', function(e) {
        const field = e.target.closest('[id="zjfd"], [id="yin1"]');
        if (field) {
            setFieldColor(field);
        }
    }, true);
}

// 增强的MutationObserver配置
function setupMutationObserver() {
    const observer = new MutationObserver(mutations => {
        let needsReinitialization = false;
        
        mutations.forEach(mutation => {
            // 检测是否有新的表格行或字段被添加
            mutation.addedNodes.forEach(node => {
                if (node.nodeType === 1) {
                    // 检查是否包含目标字段
                    if (node.querySelector && (
                        node.querySelector('[id="zjfd"]') || 
                        node.querySelector('[id="yin1"]') ||
                        node.id === 'zjfd' || 
                        node.id === 'yin1'
                    )) {
                        needsReinitialization = true;
                    }
                    
                    // 检查是否是表格相关的元素
                    if (node.tagName && (
                        node.tagName.toLowerCase() === 'tr' ||
                        node.tagName.toLowerCase() === 'tbody' ||
                        node.classList && node.classList.contains('ant-table-row')
                    )) {
                        needsReinitialization = true;
                    }
                }
            });
        });
        
        // 如果检测到需要重新初始化，使用防抖处理
        if (needsReinitialization) {
            clearTimeout(window.reinitTimeout);
            window.reinitTimeout = setTimeout(() => {
                console.log('[泛微表单] 检测到DOM变化，重新初始化...');
                initializeFields();
            }, 100);
        }
    });
    
    // 监听整个文档，确保捕获所有变化
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: false,
        characterData: false
    });
}

// 监听泛微表单特定的翻页事件
function setupPaginationListeners() {
    // 方法1：监听翻页按钮点击
    document.addEventListener('click', function(e) {
        // 检查是否点击了翻页相关的元素
        if (e.target.closest('.ant-pagination-item, .ant-pagination-prev, .ant-pagination-next')) {
            console.log('[泛微表单] 检测到翻页操作');
            setTimeout(initializeFields, 500);
        }
    }, true);
    
    // 方法2：监听表格数据变化事件（如果泛微提供）
    if (window.pageSdk) {
        // 监听表格刷新事件
        pageSdk.on('tableRefresh', () => {
            console.log('[泛微表单] 表格刷新事件触发');
            setTimeout(initializeFields, 300);
        });
        
        // 监听数据加载完成事件
        pageSdk.on('dataLoaded', () => {
            console.log('[泛微表单] 数据加载完成');
            setTimeout(initializeFields, 300);
        });
    }
    
    // 方法3：监听Ajax请求完成（泛微表单通常使用Ajax加载数据）
    if (window.jQuery || window.$) {
        $(document).ajaxComplete(function(event, xhr, settings) {
            // 检查是否是表格相关的请求
            if (settings.url && (
                settings.url.includes('table') || 
                settings.url.includes('page') ||
                settings.url.includes('list')
            )) {
                console.log('[泛微表单] Ajax请求完成，可能是翻页');
                setTimeout(initializeFields, 300);
            }
        });
    }
}

// 主初始化函数
function initialize() {
    console.log('[泛微表单] 开始初始化优化版本...');
    
    // 1. 设置事件委托
    setupEventDelegation();
    
    // 2. 设置MutationObserver
    setupMutationObserver();
    
    // 3. 设置翻页监听
    setupPaginationListeners();
    
    // 4. 初始化现有字段
    initializeFields();
}

// 确保在合适的时机初始化
if (window.pageSdk) {
    // 使用泛微的formReady事件
    pageSdk.on('formReady', () => {
        console.log('[泛微表单] formReady事件触发');
        initialize();
    });
} else {
    // 备用方案：DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        // 如果DOM已经加载完成，延迟执行以确保泛微框架初始化完成
        setTimeout(initialize, 1000);
    }
}

// 额外的保险措施：监听window.load事件
window.addEventListener('load', () => {
    setTimeout(() => {
        console.log('[泛微表单] window.load - 最终初始化检查');
        initializeFields();
    }, 2000);
});

// 导出函数供调试使用
window.weformOptimized = {
    initializeFields,
    parseNumber,
    setFieldColor,
    boundFields
};