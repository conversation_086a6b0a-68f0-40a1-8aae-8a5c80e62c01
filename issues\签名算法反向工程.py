import hashlib
import hmac

def test_signature_algorithms():
    """使用curl中的实际参数反向工程签名算法"""
    
    # curl中的实际参数
    consumer = "188880000002"
    timestamp = "1752637422784"
    nonce = "9c7b473eef2946b4839f4e5361250699"
    tenant_id = "100098706"
    open_id = "oRuFi5Afjw0Nn69fObkQ0AWJxaZk"
    expected_sign = "f1dce0b8cdbd52c6cb828ee96e4088c3"
    
    print(f"目标签名: {expected_sign}")
    print("=" * 50)
    
    # 测试方法1: 简单MD5连接
    def test_md5_simple():
        sign_string = f"consumer={consumer}&timestamp={timestamp}&nonce={nonce}&tenantId={tenant_id}&openId={open_id}"
        sign = hashlib.md5(sign_string.encode()).hexdigest()
        print(f"MD5简单: {sign_string}")
        print(f"结果: {sign}")
        return sign == expected_sign
    
    # 测试方法2: 参数字典序排序
    def test_md5_sorted():
        params = {
            'consumer': consumer,
            'timestamp': timestamp,
            'nonce': nonce,
            'tenantId': tenant_id,
            'openId': open_id
        }
        sorted_params = sorted(params.items())
        sign_string = '&'.join([f"{k}={v}" for k, v in sorted_params])
        sign = hashlib.md5(sign_string.encode()).hexdigest()
        print(f"MD5排序: {sign_string}")
        print(f"结果: {sign}")
        return sign == expected_sign
    
    # 测试方法3: 直接连接值
    def test_md5_values_only():
        params = {
            'consumer': consumer,
            'timestamp': timestamp,
            'nonce': nonce,
            'tenantId': tenant_id,
            'openId': open_id
        }
        sorted_params = sorted(params.items())
        sign_string = ''.join([v for k, v in sorted_params])
        sign = hashlib.md5(sign_string.encode()).hexdigest()
        print(f"MD5值连接: {sign_string}")
        print(f"结果: {sign}")
        return sign == expected_sign
    
    # 测试方法4: 键值直接连接
    def test_md5_key_value_concat():
        params = {
            'consumer': consumer,
            'timestamp': timestamp,
            'nonce': nonce,
            'tenantId': tenant_id,
            'openId': open_id
        }
        sorted_params = sorted(params.items())
        sign_string = ''.join([f"{k}{v}" for k, v in sorted_params])
        sign = hashlib.md5(sign_string.encode()).hexdigest()
        print(f"MD5键值连接: {sign_string}")
        print(f"结果: {sign}")
        return sign == expected_sign
    
    # 测试方法5: 包含额外参数
    def test_md5_with_extra_params():
        params = {
            'consumer': consumer,
            'timestamp': timestamp,
            'nonce': nonce,
            'tenantId': tenant_id,
            'openId': open_id,
            'cid': '',
            'v': '20220613'
        }
        sorted_params = sorted(params.items())
        sign_string = '&'.join([f"{k}={v}" for k, v in sorted_params])
        sign = hashlib.md5(sign_string.encode()).hexdigest()
        print(f"MD5含额外参数: {sign_string}")
        print(f"结果: {sign}")
        return sign == expected_sign
    
    # 测试方法6: 不包含空值参数
    def test_md5_no_empty_params():
        params = {
            'consumer': consumer,
            'timestamp': timestamp,
            'nonce': nonce,
            'tenantId': tenant_id,
            'openId': open_id,
            'v': '20220613'
        }
        sorted_params = sorted(params.items())
        sign_string = '&'.join([f"{k}={v}" for k, v in sorted_params])
        sign = hashlib.md5(sign_string.encode()).hexdigest()
        print(f"MD5无空参数: {sign_string}")
        print(f"结果: {sign}")
        return sign == expected_sign
    
    # 测试方法7: HMAC-SHA1 with consumer as secret
    def test_hmac_sha1():
        params = {
            'consumer': consumer,
            'timestamp': timestamp,
            'nonce': nonce,
            'tenantId': tenant_id,
            'openId': open_id
        }
        sorted_params = sorted(params.items())
        sign_string = '&'.join([f"{k}={v}" for k, v in sorted_params])
        sign = hmac.new(consumer.encode(), sign_string.encode(), hashlib.sha1).hexdigest()
        print(f"HMAC-SHA1: {sign_string}")
        print(f"结果: {sign}")
        return sign == expected_sign
    
    # 测试方法8: 特殊顺序
    def test_md5_special_order():
        # 按token中的顺序
        sign_string = f"consumer={consumer}&timestamp={timestamp}&nonce={nonce}&tenantId={tenant_id}&openId={open_id}"
        sign = hashlib.md5(sign_string.encode()).hexdigest()
        print(f"MD5特殊顺序: {sign_string}")
        print(f"结果: {sign}")
        return sign == expected_sign
    
    # 测试方法9: 包含secret key
    def test_md5_with_secret():
        secret = "your_secret_key"  # 可能的secret
        sign_string = f"consumer={consumer}&timestamp={timestamp}&nonce={nonce}&tenantId={tenant_id}&openId={open_id}&secret={secret}"
        sign = hashlib.md5(sign_string.encode()).hexdigest()
        print(f"MD5含secret: {sign_string}")
        print(f"结果: {sign}")
        return sign == expected_sign

    # 测试方法10: 可能的secret key变体
    def test_various_secrets():
        secrets = [
            "188880000002",  # consumer作为secret
            "100098706",     # tenantId作为secret
            "wxfb3ed1c5993a10ee",  # appId作为secret
            "rtmap",         # 公司名
            "secret",        # 通用secret
            "",              # 空secret
            "key",           # 简单key
            "sign",          # sign关键字
        ]

        for secret in secrets:
            # 方法1: secret + 参数字符串
            sign_string = f"consumer={consumer}&timestamp={timestamp}&nonce={nonce}&tenantId={tenant_id}&openId={open_id}"
            test_string = secret + sign_string
            sign = hashlib.md5(test_string.encode()).hexdigest()
            print(f"Secret前缀({secret}): {test_string}")
            print(f"结果: {sign}")
            if sign == expected_sign:
                return True

            # 方法2: 参数字符串 + secret
            test_string = sign_string + secret
            sign = hashlib.md5(test_string.encode()).hexdigest()
            print(f"Secret后缀({secret}): {test_string}")
            print(f"结果: {sign}")
            if sign == expected_sign:
                return True

            # 方法3: HMAC with secret
            try:
                sign = hmac.new(secret.encode(), sign_string.encode(), hashlib.md5).hexdigest()
                print(f"HMAC-MD5({secret}): {sign_string}")
                print(f"结果: {sign}")
                if sign == expected_sign:
                    return True
            except:
                pass

        return False

    # 测试方法11: 不同的参数组合
    def test_different_param_combinations():
        # 可能只包含部分参数
        param_combinations = [
            {'consumer': consumer, 'timestamp': timestamp, 'nonce': nonce},
            {'consumer': consumer, 'timestamp': timestamp, 'tenantId': tenant_id},
            {'consumer': consumer, 'nonce': nonce, 'tenantId': tenant_id},
            {'timestamp': timestamp, 'nonce': nonce, 'tenantId': tenant_id},
            {'consumer': consumer, 'timestamp': timestamp, 'nonce': nonce, 'tenantId': tenant_id},
            # 包含所有参数但不同顺序
            {'timestamp': timestamp, 'consumer': consumer, 'nonce': nonce, 'tenantId': tenant_id, 'openId': open_id},
        ]

        for i, params in enumerate(param_combinations):
            sorted_params = sorted(params.items())
            sign_string = '&'.join([f"{k}={v}" for k, v in sorted_params])
            sign = hashlib.md5(sign_string.encode()).hexdigest()
            print(f"组合{i+1}: {sign_string}")
            print(f"结果: {sign}")
            if sign == expected_sign:
                return True

        return False

    # 测试方法12: 原始字符串变体
    def test_raw_string_variants():
        # 测试不同的连接方式
        variants = [
            f"{consumer}{timestamp}{nonce}{tenant_id}{open_id}",  # 直接连接
            f"{consumer}|{timestamp}|{nonce}|{tenant_id}|{open_id}",  # 管道分隔
            f"{consumer},{timestamp},{nonce},{tenant_id},{open_id}",  # 逗号分隔
            f"{consumer}_{timestamp}_{nonce}_{tenant_id}_{open_id}",  # 下划线分隔
            f"consumer{consumer}timestamp{timestamp}nonce{nonce}tenantId{tenant_id}openId{open_id}",  # 带标签
        ]

        for i, variant in enumerate(variants):
            sign = hashlib.md5(variant.encode()).hexdigest()
            print(f"变体{i+1}: {variant}")
            print(f"结果: {sign}")
            if sign == expected_sign:
                return True

        return False
    
    # 执行所有测试
    tests = [
        ("MD5简单连接", test_md5_simple),
        ("MD5参数排序", test_md5_sorted),
        ("MD5值连接", test_md5_values_only),
        ("MD5键值连接", test_md5_key_value_concat),
        ("MD5含额外参数", test_md5_with_extra_params),
        ("MD5无空参数", test_md5_no_empty_params),
        ("HMAC-SHA1", test_hmac_sha1),
        ("MD5特殊顺序", test_md5_special_order),
        ("MD5含secret", test_md5_with_secret),
        ("各种Secret测试", test_various_secrets),
        ("参数组合测试", test_different_param_combinations),
        ("字符串变体测试", test_raw_string_variants),
    ]
    
    for test_name, test_func in tests:
        print(f"\n=== 测试: {test_name} ===")
        try:
            if test_func():
                print(f"✅ 找到正确算法: {test_name}")
                return test_name
            else:
                print("❌ 签名不匹配")
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n❌ 所有测试都失败了，需要更多信息")
    return None

if __name__ == "__main__":
    print("=== 大悦城签名算法反向工程 ===")
    result = test_signature_algorithms()
    if result:
        print(f"\n🎉 成功找到签名算法: {result}")
    else:
        print("\n😞 未能找到正确的签名算法")
