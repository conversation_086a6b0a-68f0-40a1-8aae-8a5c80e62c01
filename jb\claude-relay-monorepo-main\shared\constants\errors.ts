/**
 * 错误处理 - 最简实现
 */

// 错误类型
export const ERROR_TYPES = {
  INVALID_REQUEST: 'INVALID_REQUEST',
  UNAUTHORIZED: 'UNAUTHORIZED',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_EXCHANGE_FAILED: 'TOKEN_EXCHANGE_FAILED',
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  INTERNAL_ERROR: 'INTERNAL_ERROR'
} as const

export type ErrorType = typeof ERROR_TYPES[keyof typeof ERROR_TYPES]

// 错误消息映射
export const ERROR_MESSAGES: Record<ErrorType, string> = {
  [ERROR_TYPES.INVALID_REQUEST]: '请求格式无效',
  [ERROR_TYPES.UNAUTHORIZED]: '未授权访问',
  [ERROR_TYPES.TOKEN_EXPIRED]: '令牌已过期',
  [ERROR_TYPES.TOKEN_EXCHANGE_FAILED]: '令牌交换失败',
  [ERROR_TYPES.RESOURCE_NOT_FOUND]: '资源不存在',
  [ERROR_TYPES.INTERNAL_ERROR]: '服务器内部错误'
} as const

// HTTP 状态码映射
export const ERROR_STATUS_CODES: Record<ErrorType, number> = {
  [ERROR_TYPES.INVALID_REQUEST]: 400,
  [ERROR_TYPES.UNAUTHORIZED]: 401,
  [ERROR_TYPES.TOKEN_EXPIRED]: 401,
  [ERROR_TYPES.TOKEN_EXCHANGE_FAILED]: 401,
  [ERROR_TYPES.RESOURCE_NOT_FOUND]: 404,
  [ERROR_TYPES.INTERNAL_ERROR]: 500
} as const