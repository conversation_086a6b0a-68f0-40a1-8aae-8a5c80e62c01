// 泛微表单翻页优化超精简版 - 50行以内
(function() {
    // 配置区 - 便于其他代码复用
    const CONFIG = {
        tables: ['mxb1', 'mxb2'],      // 明细表名称
        standardFields: ['zjfd'],       // 标准字段：有WeFormSDK绑定
        domOnlyFields: ['yin1'],        // 仅DOM操作字段：无SDK绑定
        get allFields() { return [...this.standardFields, ...this.domOnlyFields]; }  // 所有字段
    };
    
    // 核心方法：解析数字并设置颜色
    const setColor = (field) => {
        const el = field.querySelector("input, span") || field;
        const val = (el.textContent || el.value || '').match(/[+-]?([0-9,]*\.?[0-9]+)/);
        field.style.backgroundColor = val && +val[0].replace(/,/g, '') > 0 ? "red" : "";
    };
    
    // 核心方法：初始化所有字段
    const init = () => {
        CONFIG.allFields.forEach(id =>
            document.querySelectorAll(`[id="${id}"]`).forEach(setColor)
        );
        
        // WeFormSDK事件绑定（仅绑定标准字段，避免重复）
        if (window.WeFormSDK && !window._boundTables) {
            window._boundTables = true;
            const sdk = window.WeFormSDK.getWeFormInstance();
            CONFIG.tables.forEach(table => {
                CONFIG.standardFields.forEach(field => {
                    const mark = sdk.convertFieldNameToId(field, sdk.convertFieldNameToId(table));
                    sdk.bindFieldChangeEvent(mark, (data) =>
                        document.querySelectorAll(`[fieldid^="${data.id.slice(5)}"]`).forEach(setColor)
                    );
                });
            });
        }
    };
    
    // 核心方法1：事件委托 - 处理动态元素
    ['input', 'change'].forEach(type =>
        document.addEventListener(type, (e) => {
            const field = e.target.closest(CONFIG.allFields.map(f => `[id="${f}"]`).join(','));
            field && setColor(field);
        }, true)
    );
    
    // 核心方法2：MutationObserver - 监听DOM变化
    new MutationObserver(() => init()).observe(document.body, { 
        childList: true, 
        subtree: true 
    });
    
    // 初始化
    document.readyState === 'loading' ? document.addEventListener('DOMContentLoaded', init) : init();
})();