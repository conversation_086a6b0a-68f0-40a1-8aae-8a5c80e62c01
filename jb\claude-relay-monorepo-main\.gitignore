# Dependencies
node_modules/
.pnpm-store/

# Build outputs
dist/
.output/
.nuxt/
.nitro/
.cache/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Environment files
.env
.env.local
.env.production
.env.development

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# Claude Code
.claude/

# Cloudflare
.wrangler/
.dev.vars

# Testing
coverage/
.nyc_output/

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Package manager files
yarn.lock
pnpm-lock.yaml
