// 设备类定点表单翻页颜色优化版 - 基于q_ultra_compact.js机制
(function() {
    // 配置区 - 便于维护和扩展
    const CONFIG = {
        tables: ['ft_1066780_gysbj'],           // 明细表名称
        standardFields: ['ddsl', 'gysmc', 'mlbj', 'jspf'], // 标准字段：有WeFormSDK绑定
        domOnlyFields: ['color1', 'color2', 'color3', 'color4'], // 仅DOM操作字段：无SDK绑定
        // 业务字段名到DOM id的映射表
        fieldMapping: {
            'ddsl': 'color1',    // 定点数量
            'gysmc': 'color2',   // 供应商名称
            'mlbj': 'color3',    // 目录标记
            'jspf': 'color4'     // 技术评分
        },
        get allFields() { return [...this.standardFields, ...this.domOnlyFields]; }  // 所有字段
    };

    // 保持原有的数字解析函数
    function parseNumber(value) {
        if (value === null || value === undefined || value === '') {
            return null;
        }
        
        let str = String(value);
        const matches = str.match(/[+-]?([0-9,]*\.?[0-9]+)/);
        if (!matches) {
            return null;
        }
        
        let numberStr = matches[0];
        numberStr = numberStr.replace(/,/g, '');
        const result = Number(numberStr);
        if (isNaN(result)) {
            return null;
        }
        return result;
    }

    // 核心方法：设置单个字段的颜色（基于q_ultra_compact.js的setColor模式）
    const setColor = (field) => {
        const fieldId = field.getAttribute('id');
        
        // 根据字段ID或业务字段名处理不同的颜色逻辑
        switch (fieldId) {
            case 'color1':
            case 'ddsl':
                // 处理定点数量字段（ddsl对应color1），影响同行的color2和color3
                handleDdslField(field);
                break;
                
            case 'color2':
            case 'gysmc':
                // 处理供应商名称字段（gysmc对应color2），检查是否包含"代理报价"
                handleGysmcField(field);
                break;
                
            case 'color3':
            case 'mlbj':
                // 处理目录标记字段（mlbj对应color3）
                handleMlbjField(field);
                break;
                
            case 'color4':
            case 'jspf':
                // 处理技术评分字段（jspf对应color4）
                handleJspfField(field);
                break;
        }
    };
    
    // 处理定点数量字段的颜色逻辑
    const handleDdslField = (field) => {
        const el = field.querySelector("input, span") || field;
        const value = el.textContent || el.value || '';
        const numValue = parseNumber(value);
        
        // 找到同行的其他字段
        const tr = field.closest('tr');
        if (tr) {
            const supplierNameField = tr.querySelector('[id="color2"]');
            const finalBidField = tr.querySelector('[id="color3"]');
            
            if (supplierNameField && finalBidField) {
                const supplierNameTd = supplierNameField.closest('td') || supplierNameField;
                const finalBidTd = finalBidField.closest('td') || finalBidField;
                
                // 重置背景色
                supplierNameTd.style.backgroundColor = "";
                finalBidTd.style.backgroundColor = "";
                
                // 如果定点数量大于0，设置绿色背景
                if (numValue !== null && numValue > 0.0) {
                    supplierNameTd.style.backgroundColor = "#E1FAEF";
                    finalBidTd.style.backgroundColor = "#E1FAEF";
                }
            }
        }
    };
    
    // 处理供应商名称字段的颜色逻辑
    const handleGysmcField = (field) => {
        const el = field.querySelector("span") || field;
        const value = el.textContent || el.value || "";
        
        // 重置样式
        el.style.color = "";
        el.style.fontWeight = "";
        
        if (value.includes("代理报价")) {
            el.style.color = "red";
            el.style.fontWeight = "bold";
        }
    };
    
    // 处理目录标记字段的颜色逻辑
    const handleMlbjField = (field) => {
        const el = field.querySelector("input, span") || field;
        const value = el.textContent || el.value || '';
        
        // 重置样式
        el.style.backgroundColor = "";
        el.style.color = "";
        
        // 根据目录标记值设置不同颜色（可根据实际业务需求调整）
        if (value.trim()) {
            // 如果有目录标记，设置浅蓝色背景
            el.style.backgroundColor = "#E3F2FD";
        }
    };
    
    // 处理技术评分字段的颜色逻辑
    const handleJspfField = (field) => {
        const el = field.querySelector("input, span") || field;
        const value = el.textContent || el.value || '';
        const numValue = parseNumber(value);
        
        // 重置颜色
        const targetEl = field.querySelector("span") || field;
        targetEl.style.color = "";
        
        // 如果技术评分值小于6，设置红色字体
        if (numValue !== null && numValue < 6) {
            targetEl.style.color = "#FF0000";
        }
    };

    // 核心方法：初始化所有字段（基于q_ultra_compact.js的init模式）
    const init = () => {
        CONFIG.allFields.forEach(id =>
            document.querySelectorAll(`[id="${id}"]`).forEach(setColor)
        );
        
        // WeFormSDK事件绑定（仅绑定标准字段，避免重复）
        if (window.WeFormSDK && !window._boundTables) {
            window._boundTables = true;
            const sdk = window.WeFormSDK.getWeFormInstance();
            CONFIG.tables.forEach(table => {
                CONFIG.standardFields.forEach(field => {
                    const mark = sdk.convertFieldNameToId(field, sdk.convertFieldNameToId(table));
                    sdk.bindFieldChangeEvent(mark, (data) => {
                        // 使用fieldid属性查找元素（基于q_ultra_compact.js模式）
                        document.querySelectorAll(`[fieldid^="${data.id.slice(5)}"]`).forEach(el => {
                            const fieldElement = el.closest('[id]');
                            if (fieldElement) {
                                const fieldId = fieldElement.getAttribute('id');
                                // 检查是否为配置的字段（包括业务字段名和DOM id）
                                if (CONFIG.allFields.includes(fieldId) ||
                                    CONFIG.standardFields.includes(fieldId) ||
                                    Object.values(CONFIG.fieldMapping).includes(fieldId)) {
                                    setColor(fieldElement);
                                }
                                
                                // 同时处理映射的DOM元素
                                const mappedId = CONFIG.fieldMapping[field];
                                if (mappedId && fieldId !== mappedId) {
                                    const mappedElement = document.querySelector(`[id="${mappedId}"]`);
                                    if (mappedElement) {
                                        setColor(mappedElement);
                                    }
                                }
                            }
                        });
                    });
                });
            });
        }
    };
    
    // 核心方法1：事件委托 - 处理动态元素（基于q_ultra_compact.js模式）
    ['input', 'change'].forEach(type =>
        document.addEventListener(type, (e) => {
            // 构建所有可能的字段选择器（包括业务字段名和DOM id）
            const allSelectors = [
                ...CONFIG.allFields.map(f => `[id="${f}"]`),
                ...CONFIG.standardFields.map(f => `[id="${f}"]`),
                ...Object.keys(CONFIG.fieldMapping).map(f => `[id="${f}"]`),
                ...Object.values(CONFIG.fieldMapping).map(f => `[id="${f}"]`)
            ];
            
            const field = e.target.closest(allSelectors.join(','));
            if (field) {
                setColor(field);
                
                // 如果是业务字段名，同时处理对应的DOM元素
                const fieldId = field.getAttribute('id');
                const mappedId = CONFIG.fieldMapping[fieldId];
                if (mappedId && fieldId !== mappedId) {
                    const mappedElement = document.querySelector(`[id="${mappedId}"]`);
                    if (mappedElement) {
                        setColor(mappedElement);
                    }
                }
            }
        }, true)
    );
    
    // 核心方法2：MutationObserver - 监听DOM变化（基于q_ultra_compact.js模式）
    new MutationObserver(() => init()).observe(document.body, { 
        childList: true, 
        subtree: true 
    });

    // 保持原有的formReady事件监听
    if (window.ebuilderSDK) {
        const pageSdk = window.ebuilderSDK.getPageSDK();
        if (pageSdk) {
            pageSdk.on('formReady', () => {
                setTimeout(() => {
                    init();
                }, 3000);
            });
        }
    }
    
    // 初始化（基于q_ultra_compact.js模式）
    document.readyState === 'loading' ? document.addEventListener('DOMContentLoaded', init) : init();

    // 兼容性：如果WeFormSDK已加载，立即初始化
    if (window.WeFormSDK) {
        console.log("设备类定点颜色优化已加载 - WeFormSDK ready");
    }
})();
