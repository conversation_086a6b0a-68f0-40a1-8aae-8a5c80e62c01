#!/usr/bin/env python3
"""
联通查询系统 - 简单HTTP服务器
提供静态文件和API服务
"""

#!/usr/bin/env python3
"""
联通查询系统 - 简单HTTP服务器
提供静态文件和API服务
"""

import os
import sys
import time

# 导入统一时区配置模块
from timezone_config import setup_timezone, ShanghaiTimeFormatter

# 设置时区
setup_timezone()

# 然后导入其他模块
import http.server
import socketserver
import threading
import json
from urllib.parse import urlparse, parse_qs
import logging

# 导入datetime模块
import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# 设置自定义格式器
logger = logging.getLogger(__name__)
formatter = ShanghaiTimeFormatter('%(asctime)s - %(levelname)s - %(message)s')

# 为所有处理器设置格式器
for handler in logging.getLogger().handlers:
    handler.setFormatter(formatter)

# 导入数据库模块
try:
    from database import (save_query_results, get_all_results, get_statistics,
                         has_registered_user, register_user, login_user, clear_all_users)
except ImportError:
    logger.error("无法导入database模块，请确保database.py文件存在")
    sys.exit(1)

# 导入原有的联通查询模块
try:
    from ltcx import LT, get_phone_numbers, CONFIG, query_status, query_lock
except ImportError:
    logger.warning("无法导入ltcx模块，将使用模拟数据")
    LT = None
    get_phone_numbers = None
    CONFIG = None
    # 创建本地的查询状态管理
    query_status = {}
    import threading
    query_lock = threading.Lock()

# 服务器端配置（安全存储）
SERVER_CONFIG = {
    'LOGIN_USERNAME': 'ruohan201626',
    'LOGIN_PASSWORD': 'ruohan201626'
}

class BackgroundScheduler:
    """后台定时任务调度器"""

    def __init__(self):
        self.interval_minutes = 60  # 默认1小时间隔
        self.timer = None
        self.is_running = False
        self.is_querying = False  # 是否正在查询
        self.last_query_time = None
        self.next_query_time = None
        self.lock = threading.Lock()

    def start(self):
        """启动定时任务"""
        with self.lock:
            if not self.is_running:
                self.is_running = True
                self._schedule_next_query()
                logger.info(f"后台定时查询已启动，间隔: {self.interval_minutes} 分钟")

    def stop(self):
        """停止定时任务"""
        with self.lock:
            self.is_running = False
            if self.timer:
                self.timer.cancel()
                self.timer = None
            logger.info("后台定时查询已停止")

    def set_interval(self, minutes):
        """设置查询间隔"""
        with self.lock:
            self.interval_minutes = minutes
            if self.is_running:
                # 重新调度
                if self.timer:
                    self.timer.cancel()
                self._schedule_next_query()
            logger.info(f"定时查询间隔已更新为: {minutes} 分钟")

    def _schedule_next_query(self):
        """调度下次查询"""
        if not self.is_running:
            return

        interval_seconds = self.interval_minutes * 60
        # 使用上海时区的当前时间
        from timezone_config import get_shanghai_timezone
        shanghai_tz = get_shanghai_timezone()
        current_time = datetime.datetime.now(shanghai_tz)
        self.next_query_time = current_time + datetime.timedelta(seconds=interval_seconds)

        self.timer = threading.Timer(interval_seconds, self._execute_query)
        self.timer.daemon = True
        self.timer.start()

        logger.info(f"下次自动查询时间: {self.next_query_time.strftime('%Y-%m-%d %H:%M:%S')}")

    def _execute_query(self):
        """执行查询任务"""
        try:
            logger.info("开始执行后台自动查询...")
            with self.lock:
                self.is_querying = True

            # 使用上海时区的当前时间
            from timezone_config import get_shanghai_timezone
            shanghai_tz = get_shanghai_timezone()
            self.last_query_time = datetime.datetime.now(shanghai_tz)

            if LT and get_phone_numbers:
                lt = LT()
                phones = get_phone_numbers()

                if phones:
                    logger.info(f"后台查询 {len(phones)} 个手机号...")
                    results = lt.query_all(phones)

                    if results:
                        success = save_query_results(results)
                        if success:
                            logger.info(f"后台查询完成，成功查询 {len(results)} 个账号，数据已更新到数据库")
                        else:
                            logger.error("后台查询数据保存失败")
                    else:
                        logger.warning("后台查询未获取到数据")
                else:
                    logger.warning("没有可查询的手机号")
            else:
                logger.warning("ltcx模块不可用，跳过后台查询")

        except Exception as e:
            logger.error(f"后台查询执行失败: {e}")
        finally:
            # 重置查询状态并调度下次查询
            with self.lock:
                self.is_querying = False
                if self.is_running:
                    self._schedule_next_query()

    def get_status(self):
        """获取调度器状态"""
        with self.lock:
            return {
                'is_running': self.is_running,
                'is_querying': self.is_querying,
                'interval_minutes': self.interval_minutes,
                'last_query_time': self.last_query_time.isoformat() if self.last_query_time else None,
                'next_query_time': self.next_query_time.isoformat() if self.next_query_time else None
            }

# 全局调度器实例
background_scheduler = BackgroundScheduler()

class QueryHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
        
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/' or self.path == '/index.html':
            # 返回主页面
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            try:
                with open('index.html', 'r', encoding='utf-8') as f:
                    content = f.read()
                self.wfile.write(content.encode('utf-8'))
            except FileNotFoundError:
                self.wfile.write(b'<h1>index.html not found</h1>')
                
        elif self.path == '/api/data':
            # 返回查询数据
            self.send_json_response(self.get_query_data())
            
        elif self.path == '/api/health':
            # 健康检查
            self.send_json_response({'status': 'ok', 'timestamp': time.time()})

        elif self.path == '/api/check-registration':
            # 检查注册状态
            self.check_registration_status()

        elif self.path == '/api/get_auto_query_status':
            # 获取自动查询状态
            self.get_auto_query_status()

        elif self.path == '/api/clear-users':
            # 清空用户数据（仅用于开发/测试）
            self.clear_users_data()

        else:
            # 处理其他静态文件
            super().do_GET()

    def do_POST(self):
        """处理POST请求"""
        if self.path == '/api/register':
            self.handle_register_request()
        elif self.path == '/api/login':
            self.handle_login_request()
        elif self.path == '/api/query':
            self.handle_query_request()
        elif self.path == '/api/query_status':
            self.handle_query_status_request()
        elif self.path == '/api/set_auto_query_interval':
            self.set_auto_query_interval()
        else:
            self.send_error(404, "API endpoint not found")
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

    def handle_login_request(self):
        """处理登录请求"""
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length > 0:
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode('utf-8'))
            else:
                data = {}

            username = data.get('username', '')
            password = data.get('password', '')

            # 使用数据库验证登录信息
            result = login_user(username, password)

            if result['success']:
                self.send_json_response({
                    'success': True,
                    'message': result['message'],
                    'user': result.get('user', {})
                })
            else:
                self.send_json_response({
                    'success': False,
                    'message': result['message']
                }, 401)

        except json.JSONDecodeError:
            self.send_json_response({
                'success': False,
                'message': '请求数据格式错误'
            }, 400)
        except Exception as e:
            logger.error(f"登录请求处理失败: {e}")
            self.send_json_response({
                'success': False,
                'message': f'服务器错误: {str(e)}'
            }, 500)

    def handle_register_request(self):
        """处理注册请求"""
        try:
            # 首先检查是否已有用户注册（双重检查）
            if has_registered_user():
                logger.warning("尝试注册但系统已有用户，拒绝注册")
                self.send_json_response({
                    'success': False,
                    'message': '系统已有用户注册，禁止重复注册'
                }, 403)
                return

            content_length = int(self.headers.get('Content-Length', 0))
            if content_length > 0:
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode('utf-8'))
            else:
                data = {}

            username = data.get('username', '')
            password = data.get('password', '')

            # 验证输入
            if not username or len(username.strip()) < 3:
                self.send_json_response({
                    'success': False,
                    'message': '用户名至少需要3个字符'
                }, 400)
                return

            if not password or len(password) < 6:
                self.send_json_response({
                    'success': False,
                    'message': '密码至少需要6个字符'
                }, 400)
                return

            # 使用数据库注册用户
            result = register_user(username.strip(), password)

            if result['success']:
                logger.info(f"用户注册成功: {username}")
                self.send_json_response({
                    'success': True,
                    'message': result['message']
                })
            else:
                logger.warning(f"用户注册失败: {username} - {result['message']}")
                self.send_json_response({
                    'success': False,
                    'message': result['message']
                }, 400)

        except json.JSONDecodeError:
            self.send_json_response({
                'success': False,
                'message': '请求数据格式错误'
            }, 400)
        except Exception as e:
            logger.error(f"注册请求处理失败: {e}")
            self.send_json_response({
                'success': False,
                'message': f'服务器错误: {str(e)}'
            }, 500)

    def check_registration_status(self):
        """检查注册状态"""
        try:
            has_user = has_registered_user()
            self.send_json_response({
                'success': True,
                'has_registered_user': has_user
            })
        except Exception as e:
            logger.error(f"检查注册状态失败: {e}")
            self.send_json_response({
                'success': False,
                'message': f'检查注册状态失败: {str(e)}'
            }, 500)

    def get_auto_query_status(self):
        """获取自动查询状态"""
        try:
            status = background_scheduler.get_status()
            self.send_json_response({
                'success': True,
                'data': status
            })
        except Exception as e:
            logger.error(f"获取自动查询状态失败: {e}")
            self.send_json_response({
                'success': False,
                'message': f'获取自动查询状态失败: {str(e)}'
            }, 500)

    def clear_users_data(self):
        """清空用户数据（仅用于开发/测试）"""
        try:
            success = clear_all_users()
            if success:
                logger.info("用户数据已清空")
                self.send_json_response({
                    'success': True,
                    'message': '用户数据已清空，现在可以重新注册'
                })
            else:
                self.send_json_response({
                    'success': False,
                    'message': '清空用户数据失败'
                }, 500)
        except Exception as e:
            logger.error(f"清空用户数据失败: {e}")
            self.send_json_response({
                'success': False,
                'message': f'清空用户数据失败: {str(e)}'
            }, 500)

    def handle_query_request(self):
        """处理查询请求"""
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length > 0:
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode('utf-8'))
            else:
                data = {}

            # 启动查询（移除密码验证）
            query_id = str(int(time.time()))
            logger.info(f"启动查询任务: {query_id}")

            # 初始化查询状态（使用锁确保线程安全）
            with query_lock:
                query_status[query_id] = {
                    'status': 'running',
                    'progress': 0,
                    'message': '准备查询...',
                    'success_count': 0,
                    'fail_count': 0,
                    'total_accounts': 0,
                    'processed_accounts': 0,
                    'start_time': time.time()
                }

            # 在后台线程中执行查询
            if LT and get_phone_numbers:
                threading.Thread(
                    target=self.run_query,
                    args=(query_id,),
                    daemon=True
                ).start()
            else:
                # 模拟查询
                threading.Thread(
                    target=self.run_mock_query,
                    args=(query_id,),
                    daemon=True
                ).start()

            self.send_json_response({
                'success': True,
                'query_id': query_id,
                'message': '查询已启动'
            })
            
        except json.JSONDecodeError:
            self.send_json_response({
                'success': False, 
                'message': '请求数据格式错误'
            }, 400)
        except Exception as e:
            logger.error(f"查询请求处理失败: {e}")
            self.send_json_response({
                'success': False, 
                'message': f'服务器错误: {str(e)}'
            }, 500)
    
    def handle_query_status_request(self):
        """处理查询状态请求"""
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length > 0:
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode('utf-8'))
            else:
                data = {}

            query_id = data.get('query_id', '')
            logger.info(f"查询状态: {query_id}")

            # 返回实际的查询状态
            if query_id in query_status:
                status_data = query_status[query_id]
                self.send_json_response({
                    'success': True,
                    'data': status_data
                })
            else:
                self.send_json_response({
                    'success': False,
                    'message': '查询ID不存在'
                }, 404)

        except Exception as e:
            logger.error(f"状态查询失败: {e}")
            self.send_json_response({
                'success': False,
                'message': f'状态查询失败: {str(e)}'
            }, 500)
    
    def run_query(self, query_id):
        """在后台运行查询"""
        try:
            logger.info(f"开始执行查询任务: {query_id}")

            # 更新状态：连接服务器
            with query_lock:
                query_status[query_id].update({
                    'progress': 10,
                    'message': '连接服务器...'
                })

            lt = LT()
            phones = get_phone_numbers()

            if not phones:
                with query_lock:
                    query_status[query_id].update({
                        'status': 'failed',
                        'progress': 0,
                        'message': '没有可查询的手机号'
                    })
                return

            # 更新状态：获取手机号列表
            with query_lock:
                query_status[query_id].update({
                    'progress': 25,
                    'message': f'获取到 {len(phones)} 个手机号...',
                    'total_accounts': len(phones),
                    'processed_accounts': 0
                })

            # 执行完整查询，获取所有结果
            results = lt.query_all_with_progress(phones, query_id)

            # 将查询结果保存到数据库
            if results:
                success = save_query_results(results)
                if success:
                    logger.info(f"查询任务 {query_id} 完成，数据已保存到数据库")
                else:
                    logger.error(f"查询任务 {query_id} 数据保存失败")

            # 确保查询状态已设置为完成（防止时序问题）
            with query_lock:
                if query_id in query_status:
                    query_status[query_id].update({
                        'status': 'completed',
                        'progress': 100,
                        'message': '查询完成！'
                    })

            logger.info(f"查询任务 {query_id} 完成，数据已保存到数据库")

        except Exception as e:
            logger.error(f"查询任务 {query_id} 失败: {e}")
            with query_lock:
                query_status[query_id].update({
                    'status': 'failed',
                    'progress': 0,
                    'message': f'查询失败: {str(e)}'
                })

    def run_mock_query(self, query_id):
        """运行模拟查询（当ltcx模块不可用时）"""
        try:
            steps = [
                (10, '连接服务器...'),
                (25, '验证权限...'),
                (40, '获取手机号列表...'),
                (60, '查询账户信息...'),
                (80, '处理数据...'),
                (95, '生成报告...'),
                (100, '查询完成！')
            ]

            for progress, message in steps:
                with query_lock:
                    query_status[query_id].update({
                        'progress': progress,
                        'message': message
                    })
                time.sleep(1)  # 模拟处理时间

            with query_lock:
                query_status[query_id]['status'] = 'completed'

        except Exception as e:
            logger.error(f"模拟查询任务 {query_id} 失败: {e}")
            with query_lock:
                query_status[query_id].update({
                    'status': 'failed',
                    'progress': 0,
                    'message': f'查询失败: {str(e)}'
                })
    
    def get_query_data(self):
        """获取查询数据"""
        # 从数据库获取查询结果
        latest_query_results = get_all_results()

        # 优先使用最新查询结果
        if latest_query_results:
            try:
                # 转换数据格式
                result = []
                for phone, info in latest_query_results.items():
                    if isinstance(info, dict):
                        # 显示完整手机号（不脱敏）
                        display_phone = phone

                        # 格式化话费券信息
                        vouchers_info = ''
                        if 'vouchers' in info and info['vouchers']:
                            vouchers_str = info['vouchers']
                            logger.debug(f"处理话费券数据: {repr(vouchers_str)}")

                            if isinstance(vouchers_str, str) and vouchers_str.strip():
                                # 按行分割话费券，每行一个券
                                lines = vouchers_str.split('\n')
                                voucher_lines = []
                                for line in lines:
                                    line = line.strip()
                                    if line and not line.startswith('总未使用金额'):
                                        voucher_lines.append(line)

                                # 如果有话费券，每行显示一个
                                if voucher_lines:
                                    vouchers_info = '<br>'.join(voucher_lines)

                                    # 添加总金额信息（如果有）
                                    for line in lines:
                                        if line.strip().startswith('总未使用金额'):
                                            vouchers_info += '<br>' + line.strip()
                                            break
                            elif isinstance(vouchers_str, list):
                                vouchers_info = '<br>'.join([f"{v.get('amount', 0)}元券({v.get('expire_date', '')})" for v in vouchers_str])

                            logger.debug(f"格式化后的话费券: {vouchers_info}")

                        # 格式化任务信息
                        tasks_info = ''
                        if 'tasks' in info and info['tasks']:
                            tasks_list = info['tasks']
                            if isinstance(tasks_list, list):
                                task_strings = []
                                for task in tasks_list:
                                    if isinstance(task, dict):
                                        name = task.get('name', '')
                                        current = task.get('current', 0)
                                        total = task.get('total', 0)
                                        if name and total > 0:
                                            task_strings.append(f"{name}: {current}/{total}")
                                tasks_info = ', '.join(task_strings)
                            elif isinstance(tasks_list, str):
                                tasks_info = tasks_list

                        result.append({
                            'phone': display_phone,
                            'balance': str(info.get('available', 0)),
                            'total': str(info.get('total', 0)),
                            'vouchers': vouchers_info,
                            'tasks': tasks_info,
                            'lastQuery': info.get('last_query', time.strftime('%Y-%m-%d %H:%M:%S'))
                        })

                if result:
                    logger.info(f"返回最新查询数据，共 {len(result)} 条记录")
                    return result
            except Exception as e:
                logger.error(f"处理最新查询数据失败: {e}")

        # 返回空数据，提示用户进行查询
        return []
    
    def format_tasks(self, tasks):
        """格式化任务信息"""
        if not tasks:
            return ''
        
        task_strings = []
        for task in tasks:
            if isinstance(task, dict):
                name = task.get('name', '')
                current = task.get('current', 0)
                total = task.get('total', 0)
                if name and total > 0:
                    task_strings.append(f"{name}: {current}/{total}")
        
        return ', '.join(task_strings)

    def set_auto_query_interval(self):
        """设置自动查询间隔"""
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length > 0:
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode('utf-8'))
            else:
                data = {}

            interval_minutes = data.get('interval_minutes')
            if not interval_minutes or not isinstance(interval_minutes, (int, float)) or interval_minutes <= 0:
                self.send_json_response({
                    'success': False,
                    'message': '无效的间隔时间，必须是大于0的数字（分钟）'
                }, 400)
                return

            # 设置新的间隔
            background_scheduler.set_interval(int(interval_minutes))

            self.send_json_response({
                'success': True,
                'message': f'自动查询间隔已设置为 {interval_minutes} 分钟',
                'interval_minutes': int(interval_minutes)
            })

        except json.JSONDecodeError:
            self.send_json_response({
                'success': False,
                'message': '请求数据格式错误'
            }, 400)
        except Exception as e:
            logger.error(f"设置自动查询间隔失败: {e}")
            self.send_json_response({
                'success': False,
                'message': f'设置失败: {str(e)}'
            }, 500)

    def send_json_response(self, data, status_code=200):
        """发送JSON响应"""
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(json_data.encode('utf-8'))
    
    def log_message(self, format, *args):
        """重写日志方法"""
        logger.info(f"{self.address_string()} - {format % args}")

def initial_query():
    """首次部署时自动执行查询"""
    try:
        # 检查是否为首次部署
        is_first_deploy = os.getenv('FIRST_DEPLOY', 'false').lower() == 'true'

        if not is_first_deploy:
            logger.info("非首次部署，跳过自动查询")
            return

        logger.info("检测到首次部署，执行自动查询...")

        # 等待服务器完全启动
        time.sleep(3)

        if LT and get_phone_numbers:
            lt = LT()
            phones = get_phone_numbers()

            if phones:
                logger.info(f"开始查询 {len(phones)} 个手机号...")
                results = lt.query_all(phones)

                # 将查询结果保存到数据库
                if results:
                    success = save_query_results(results)
                    if success:
                        logger.info(f"首次查询完成，成功查询 {len(results)} 个账号，数据已保存到数据库")
                    else:
                        logger.error("首次查询数据保存失败")
                else:
                    logger.warning("首次查询未获取到数据")

                logger.info("用户访问页面时将看到查询结果")
            else:
                logger.warning("没有可查询的手机号")
        else:
            logger.warning("ltcx模块不可用，跳过首次查询")

    except Exception as e:
        logger.error(f"首次查询失败: {e}")

def main():
    """主函数"""
    port = 2032

    logger.info(f"启动联通查询系统服务器")
    logger.info(f"端口: {port}")
    logger.info(f"访问地址: http://localhost:{port}")
    logger.info("按 Ctrl+C 停止服务器")

    # 在后台线程中执行首次查询
    threading.Thread(target=initial_query, daemon=True).start()

    # 启动后台定时任务
    background_scheduler.start()
    logger.info("后台定时查询调度器已启动")

    try:
        with socketserver.TCPServer(("", port), QueryHTTPRequestHandler) as httpd:
            httpd.serve_forever()
    except KeyboardInterrupt:
        logger.info("服务器已停止")
        background_scheduler.stop()
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        background_scheduler.stop()

if __name__ == "__main__":
    main()
